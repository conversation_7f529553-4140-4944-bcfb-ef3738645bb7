"""
Security Exploitation Module Package
وحدات الأمان والاختراق

This package contains modules for security testing and exploitation:
- password_cracking: Password cracking techniques
- realistic_password_cracking: Realistic password attacks
- web_exploitation_xss: XSS vulnerability exploitation
- info_stealer_educational: Educational information stealing framework
"""

__version__ = "1.0.0"
__author__ = "Botnet Lab Team"

# Import all modules for easy access
try:
    from .password_cracking import *
except ImportError:
    pass

try:
    from .realistic_password_cracking import *
except ImportError:
    pass

try:
    from .web_exploitation_xss import *
except ImportError:
    pass

try:
    from .info_stealer_educational import *
except ImportError:
    pass

__all__ = [
    'password_cracking',
    'realistic_password_cracking', 
    'web_exploitation_xss',
    'info_stealer_educational'
]
