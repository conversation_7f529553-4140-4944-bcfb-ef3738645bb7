2025-07-26 12:29:37,153 - __main__ - INFO - Framework Setup initialized
2025-07-26 12:29:37,154 - __main__ - INFO - Starting Security Exploitation Framework setup...
2025-07-26 12:29:37,154 - __main__ - INFO - Step: Checking Python version
2025-07-26 12:29:37,155 - __main__ - INFO - Python version 3.13.3 (main, Apr 10 2025, 21:38:51) [GCC 14.2.0] is supported
2025-07-26 12:29:37,155 - __main__ - INFO - Step: Creating directories
2025-07-26 12:29:37,156 - __main__ - INFO - Created directory: data/databases
2025-07-26 12:29:37,157 - __main__ - INFO - Created directory: data/cache
2025-07-26 12:29:37,157 - __main__ - INFO - Created directory: data/keys
2025-07-26 12:29:37,157 - __main__ - INFO - Created directory: data/wordlists
2025-07-26 12:29:37,157 - __main__ - INFO - Created directory: data/signatures
2025-07-26 12:29:37,157 - __main__ - INFO - Created directory: data/intelligence
2025-07-26 12:29:37,158 - __main__ - INFO - Created directory: logs
2025-07-26 12:29:37,158 - __main__ - INFO - Created directory: reports
2025-07-26 12:29:37,159 - __main__ - INFO - Created directory: templates/reports
2025-07-26 12:29:37,159 - __main__ - INFO - Created directory: templates/configs
2025-07-26 12:29:37,159 - __main__ - INFO - Created directory: templates/workflows
2025-07-26 12:29:37,160 - __main__ - INFO - Step: Installing requirements
2025-07-26 12:29:37,160 - __main__ - INFO - Installing Python requirements...
2025-07-26 12:29:37,901 - __main__ - ERROR - Error installing requirements: Command '['/usr/bin/python', '-m', 'pip', 'install', '--upgrade', 'pip']' returned non-zero exit status 1.
2025-07-26 12:29:37,902 - __main__ - ERROR - Setup failed at step: Installing requirements
