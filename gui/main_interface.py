#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرسومية الرئيسية
Main GUI Interface for Security Framework
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import threading
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd

# إضافة مسار المشروع للاستيراد
import sys
import os
sys.path.append(str(os.path.dirname(os.path.dirname(__file__))))

from core.config_manager import config
from core.database_manager import db_manager
from core.security_manager import security_manager
from modules.reconnaissance.basic_recon import BasicReconModule
from modules.reconnaissance.advanced_osint import AdvancedOSINTModule
from modules.exploitation.web_exploitation import WebExploitationModule
from modules.intelligence.ai_engine import AISecurityEngine

class SecurityFrameworkGUI:
    """الواجهة الرسومية الرئيسية لإطار الأمان"""
    
    def __init__(self):
        """تهيئة الواجهة الرسومية"""
        self.root = tk.Tk()
        self.root.title("Security Exploitation Framework - إطار عمل استغلال الأمان")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # متغيرات الحالة
        self.current_session = None
        self.running_operations = {}
        self.results_data = {}
        
        # إعداد التسجيل
        self.setup_logging()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل الإعدادات
        self.load_configuration()
        
        self.logger.info("Security Framework GUI initialized")
    
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def create_interface(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء القائمة الرئيسية
        self.create_menu()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء النوافذ الرئيسية
        self.create_main_panels()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_menu(self):
        """إنشاء القائمة الرئيسية"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جلسة جديدة", command=self.new_session)
        file_menu.add_command(label="فتح جلسة", command=self.open_session)
        file_menu.add_command(label="حفظ الجلسة", command=self.save_session)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير النتائج", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="الاستطلاع الأساسي", command=self.launch_basic_recon)
        tools_menu.add_command(label="OSINT المتقدم", command=self.launch_advanced_osint)
        tools_menu.add_command(label="استغلال الويب", command=self.launch_web_exploitation)
        tools_menu.add_command(label="الذكاء الاصطناعي", command=self.launch_ai_analysis)
        
        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="لوحة المعلومات", command=self.show_dashboard)
        view_menu.add_command(label="السجلات", command=self.show_logs)
        view_menu.add_command(label="الإحصائيات", command=self.show_statistics)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(side=tk.TOP, fill=tk.X, padx=5, pady=2)
        
        # أزرار الأدوات الرئيسية
        ttk.Button(toolbar, text="جلسة جديدة", command=self.new_session).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="استطلاع", command=self.launch_basic_recon).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="استغلال", command=self.launch_web_exploitation).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="تحليل ذكي", command=self.launch_ai_analysis).pack(side=tk.LEFT, padx=2)
        
        # فاصل
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # معلومات الجلسة
        ttk.Label(toolbar, text="الجلسة:").pack(side=tk.LEFT, padx=2)
        self.session_label = ttk.Label(toolbar, text="غير متصل", foreground="red")
        self.session_label.pack(side=tk.LEFT, padx=2)
    
    def create_main_panels(self):
        """إنشاء النوافذ الرئيسية"""
        # إنشاء النافذة المقسمة
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # النافذة اليسرى - التحكم والإعدادات
        left_frame = ttk.Frame(main_paned, width=300)
        main_paned.add(left_frame, weight=1)
        
        # النافذة اليمنى - النتائج والعرض
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=3)
        
        # إنشاء محتوى النافذة اليسرى
        self.create_control_panel(left_frame)
        
        # إنشاء محتوى النافذة اليمنى
        self.create_results_panel(right_frame)
    
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        # عنوان اللوحة
        ttk.Label(parent, text="لوحة التحكم", font=("Arial", 12, "bold")).pack(pady=5)
        
        # إعدادات الهدف
        target_frame = ttk.LabelFrame(parent, text="إعدادات الهدف", padding=10)
        target_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(target_frame, text="الهدف:").pack(anchor=tk.W)
        self.target_entry = ttk.Entry(target_frame, width=30)
        self.target_entry.pack(fill=tk.X, pady=2)
        
        ttk.Label(target_frame, text="نوع الهدف:").pack(anchor=tk.W, pady=(10,0))
        self.target_type = ttk.Combobox(target_frame, values=["IP Address", "Domain", "URL", "Email"])
        self.target_type.pack(fill=tk.X, pady=2)
        
        # إعدادات العملية
        operation_frame = ttk.LabelFrame(parent, text="إعدادات العملية", padding=10)
        operation_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.deep_scan_var = tk.BooleanVar()
        ttk.Checkbutton(operation_frame, text="فحص عميق", variable=self.deep_scan_var).pack(anchor=tk.W)
        
        self.save_results_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(operation_frame, text="حفظ النتائج", variable=self.save_results_var).pack(anchor=tk.W)
        
        self.ai_analysis_var = tk.BooleanVar()
        ttk.Checkbutton(operation_frame, text="تحليل ذكي", variable=self.ai_analysis_var).pack(anchor=tk.W)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(buttons_frame, text="بدء الاستطلاع", command=self.start_reconnaissance).pack(fill=tk.X, pady=2)
        ttk.Button(buttons_frame, text="بدء الاستغلال", command=self.start_exploitation).pack(fill=tk.X, pady=2)
        ttk.Button(buttons_frame, text="تحليل النتائج", command=self.analyze_results).pack(fill=tk.X, pady=2)
        
        # شريط التقدم
        ttk.Label(parent, text="التقدم:").pack(anchor=tk.W, padx=5, pady=(10,0))
        self.progress_bar = ttk.Progressbar(parent, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=5, pady=2)
        
        self.progress_label = ttk.Label(parent, text="جاهز")
        self.progress_label.pack(anchor=tk.W, padx=5)
    
    def create_results_panel(self, parent):
        """إنشاء لوحة النتائج"""
        # إنشاء النوافذ المبوبة
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب النتائج
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="النتائج")
        
        # تبويب الرسوم البيانية
        charts_frame = ttk.Frame(notebook)
        notebook.add(charts_frame, text="الرسوم البيانية")
        
        # تبويب السجلات
        logs_frame = ttk.Frame(notebook)
        notebook.add(logs_frame, text="السجلات")
        
        # إنشاء محتوى تبويب النتائج
        self.create_results_tree(results_frame)
        
        # إنشاء محتوى تبويب الرسوم البيانية
        self.create_charts_panel(charts_frame)
        
        # إنشاء محتوى تبويب السجلات
        self.create_logs_panel(logs_frame)
    
    def create_results_tree(self, parent):
        """إنشاء شجرة النتائج"""
        # إنشاء Treeview للنتائج
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # شجرة النتائج
        self.results_tree = ttk.Treeview(tree_frame, yscrollcommand=scrollbar.set)
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.results_tree.yview)
        
        # تعريف الأعمدة
        self.results_tree["columns"] = ("type", "target", "status", "timestamp")
        self.results_tree.column("#0", width=200, minwidth=150)
        self.results_tree.column("type", width=100, minwidth=80)
        self.results_tree.column("target", width=200, minwidth=150)
        self.results_tree.column("status", width=80, minwidth=60)
        self.results_tree.column("timestamp", width=150, minwidth=120)
        
        # تعريف العناوين
        self.results_tree.heading("#0", text="العملية", anchor=tk.W)
        self.results_tree.heading("type", text="النوع", anchor=tk.W)
        self.results_tree.heading("target", text="الهدف", anchor=tk.W)
        self.results_tree.heading("status", text="الحالة", anchor=tk.W)
        self.results_tree.heading("timestamp", text="الوقت", anchor=tk.W)
        
        # ربط الأحداث
        self.results_tree.bind("<Double-1>", self.on_result_double_click)
    
    def create_charts_panel(self, parent):
        """إنشاء لوحة الرسوم البيانية"""
        # إنشاء إطار للرسوم البيانية
        self.charts_frame = ttk.Frame(parent)
        self.charts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # رسم بياني افتراضي
        self.create_default_chart()
    
    def create_default_chart(self):
        """إنشاء رسم بياني افتراضي"""
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.bar(['الاستطلاع', 'الاستغلال', 'التحليل'], [0, 0, 0])
        ax.set_title('إحصائيات العمليات')
        ax.set_ylabel('عدد العمليات')
        
        # إدراج الرسم في الواجهة
        canvas = FigureCanvasTkAgg(fig, self.charts_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def create_logs_panel(self, parent):
        """إنشاء لوحة السجلات"""
        # إنشاء منطقة نص للسجلات
        logs_frame = ttk.Frame(parent)
        logs_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # شريط التمرير للسجلات
        logs_scrollbar = ttk.Scrollbar(logs_frame)
        logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # منطقة نص السجلات
        self.logs_text = tk.Text(logs_frame, yscrollcommand=logs_scrollbar.set, 
                                font=("Courier", 10), bg="#1e1e1e", fg="#ffffff")
        self.logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        logs_scrollbar.config(command=self.logs_text.yview)
        
        # إضافة سجل ترحيبي
        self.add_log("INFO", "Security Framework GUI started")
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # معلومات إضافية
        self.connection_label = ttk.Label(status_frame, text="غير متصل", relief=tk.SUNKEN)
        self.connection_label.pack(side=tk.RIGHT)
    
    def load_configuration(self):
        """تحميل الإعدادات"""
        try:
            if config.load_config('config.yaml'):
                self.add_log("INFO", "Configuration loaded successfully")
                self.update_status("الإعدادات محملة")
            else:
                self.add_log("WARNING", "Failed to load configuration")
                self.update_status("فشل تحميل الإعدادات")
        except Exception as e:
            self.add_log("ERROR", f"Configuration error: {e}")
    
    def new_session(self):
        """إنشاء جلسة جديدة"""
        try:
            self.current_session = db_manager.create_session("gui_user")
            self.session_label.config(text=f"الجلسة: {self.current_session[:8]}", foreground="green")
            self.add_log("INFO", f"New session created: {self.current_session}")
            self.update_status("جلسة جديدة تم إنشاؤها")
        except Exception as e:
            self.add_log("ERROR", f"Failed to create session: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء الجلسة: {e}")
    
    def start_reconnaissance(self):
        """بدء عملية الاستطلاع"""
        target = self.target_entry.get().strip()
        if not target:
            messagebox.showwarning("تحذير", "يرجى إدخال هدف صحيح")
            return
        
        if not self.current_session:
            self.new_session()
        
        # تشغيل الاستطلاع في خيط منفصل
        thread = threading.Thread(target=self._run_reconnaissance, args=(target,))
        thread.daemon = True
        thread.start()
    
    def _run_reconnaissance(self, target):
        """تشغيل الاستطلاع"""
        try:
            self.update_progress(0, "بدء الاستطلاع...")
            
            # إنشاء وحدة الاستطلاع
            recon_module = BasicReconModule()
            recon_module.set_authorization(True, True)
            recon_module.set_session(self.current_session)
            
            # تشغيل الاستطلاع
            results = recon_module.start_execution(target)
            
            # إضافة النتائج للواجهة
            self.add_result("استطلاع أساسي", target, "مكتمل", results)
            
            self.update_progress(100, "اكتمل الاستطلاع")
            self.add_log("INFO", f"Reconnaissance completed for {target}")
            
        except Exception as e:
            self.add_log("ERROR", f"Reconnaissance failed: {e}")
            self.update_progress(0, "فشل الاستطلاع")
    
    def start_exploitation(self):
        """بدء عملية الاستغلال"""
        target = self.target_entry.get().strip()
        if not target:
            messagebox.showwarning("تحذير", "يرجى إدخال هدف صحيح")
            return
        
        if not self.current_session:
            self.new_session()
        
        # تأكيد الاستغلال
        if not messagebox.askyesno("تأكيد", "هل أنت متأكد من بدء عملية الاستغلال؟\nتأكد من وجود تصريح قانوني."):
            return
        
        # تشغيل الاستغلال في خيط منفصل
        thread = threading.Thread(target=self._run_exploitation, args=(target,))
        thread.daemon = True
        thread.start()
    
    def _run_exploitation(self, target):
        """تشغيل الاستغلال"""
        try:
            self.update_progress(0, "بدء الاستغلال...")
            
            # إنشاء وحدة الاستغلال
            exploit_module = WebExploitationModule()
            exploit_module.set_authorization(True, True)
            exploit_module.set_session(self.current_session)
            
            # تشغيل الاستغلال
            results = exploit_module.start_execution(target)
            
            # إضافة النتائج للواجهة
            self.add_result("استغلال الويب", target, "مكتمل", results)
            
            self.update_progress(100, "اكتمل الاستغلال")
            self.add_log("INFO", f"Exploitation completed for {target}")
            
        except Exception as e:
            self.add_log("ERROR", f"Exploitation failed: {e}")
            self.update_progress(0, "فشل الاستغلال")
    
    def analyze_results(self):
        """تحليل النتائج باستخدام الذكاء الاصطناعي"""
        if not self.results_data:
            messagebox.showinfo("معلومات", "لا توجد نتائج للتحليل")
            return
        
        thread = threading.Thread(target=self._run_ai_analysis)
        thread.daemon = True
        thread.start()
    
    def _run_ai_analysis(self):
        """تشغيل التحليل الذكي"""
        try:
            self.update_progress(0, "بدء التحليل الذكي...")
            
            # إنشاء محرك الذكاء الاصطناعي
            ai_engine = AISecurityEngine()
            ai_engine.set_authorization(True, True)
            
            # تحضير البيانات للتحليل
            analysis_data = self._prepare_analysis_data()
            
            # تشغيل التحليل
            results = ai_engine.start_execution("comprehensive_analysis", **analysis_data)
            
            # إضافة النتائج للواجهة
            self.add_result("تحليل ذكي", "شامل", "مكتمل", results)
            
            self.update_progress(100, "اكتمل التحليل الذكي")
            self.add_log("INFO", "AI analysis completed")
            
        except Exception as e:
            self.add_log("ERROR", f"AI analysis failed: {e}")
            self.update_progress(0, "فشل التحليل الذكي")
    
    def _prepare_analysis_data(self) -> Dict[str, Any]:
        """تحضير البيانات للتحليل الذكي"""
        analysis_data = {}
        
        # جمع بيانات الثغرات
        vulnerabilities = []
        for result in self.results_data.values():
            if result.get('data', {}).get('vulnerabilities'):
                vulnerabilities.extend(result['data']['vulnerabilities'])
        
        if vulnerabilities:
            analysis_data['vulnerability_data'] = {'vulnerabilities': vulnerabilities}
        
        return analysis_data
    
    def add_result(self, operation_type: str, target: str, status: str, data: Dict):
        """إضافة نتيجة جديدة"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        result_id = f"{operation_type}_{target}_{timestamp}"
        
        # إضافة للبيانات
        self.results_data[result_id] = {
            'type': operation_type,
            'target': target,
            'status': status,
            'timestamp': timestamp,
            'data': data
        }
        
        # إضافة للشجرة
        self.results_tree.insert("", "end", text=operation_type, 
                                values=(operation_type, target, status, timestamp))
    
    def add_log(self, level: str, message: str):
        """إضافة سجل جديد"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.logs_text.insert(tk.END, log_entry)
        self.logs_text.see(tk.END)
    
    def update_progress(self, value: int, text: str = ""):
        """تحديث شريط التقدم"""
        self.progress_bar['value'] = value
        if text:
            self.progress_label.config(text=text)
        self.root.update_idletasks()
    
    def update_status(self, text: str):
        """تحديث شريط الحالة"""
        self.status_label.config(text=text)
    
    def on_result_double_click(self, event):
        """معالج النقر المزدوج على النتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            operation = item['text']
            values = item['values']
            
            # عرض تفاصيل النتيجة
            self.show_result_details(operation, values)
    
    def show_result_details(self, operation: str, values: List):
        """عرض تفاصيل النتيجة"""
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل: {operation}")
        details_window.geometry("600x400")
        
        # منطقة نص للتفاصيل
        text_widget = tk.Text(details_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # البحث عن البيانات المفصلة
        result_key = f"{operation}_{values[1]}_{values[3]}"
        if result_key in self.results_data:
            details = json.dumps(self.results_data[result_key]['data'], 
                               indent=2, ensure_ascii=False)
            text_widget.insert(tk.END, details)
    
    # الوظائف الإضافية للقوائم
    def launch_basic_recon(self):
        """تشغيل الاستطلاع الأساسي"""
        self.start_reconnaissance()
    
    def launch_advanced_osint(self):
        """تشغيل OSINT المتقدم"""
        messagebox.showinfo("معلومات", "OSINT المتقدم - قيد التطوير")
    
    def launch_web_exploitation(self):
        """تشغيل استغلال الويب"""
        self.start_exploitation()
    
    def launch_ai_analysis(self):
        """تشغيل التحليل الذكي"""
        self.analyze_results()
    
    def show_dashboard(self):
        """عرض لوحة المعلومات"""
        messagebox.showinfo("معلومات", "لوحة المعلومات - قيد التطوير")
    
    def show_logs(self):
        """عرض السجلات"""
        messagebox.showinfo("معلومات", "عرض السجلات - متاح في التبويب")
    
    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            stats = db_manager.get_database_statistics()
            stats_text = json.dumps(stats, indent=2, ensure_ascii=False)
            
            stats_window = tk.Toplevel(self.root)
            stats_window.title("إحصائيات النظام")
            stats_window.geometry("500x400")
            
            text_widget = tk.Text(stats_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, stats_text)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات: {e}")
    
    def open_session(self):
        """فتح جلسة محفوظة"""
        messagebox.showinfo("معلومات", "فتح الجلسة - قيد التطوير")
    
    def save_session(self):
        """حفظ الجلسة الحالية"""
        messagebox.showinfo("معلومات", "حفظ الجلسة - قيد التطوير")
    
    def export_results(self):
        """تصدير النتائج"""
        if not self.results_data:
            messagebox.showinfo("معلومات", "لا توجد نتائج للتصدير")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.results_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("نجح", f"تم تصدير النتائج إلى: {filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في التصدير: {e}")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", 
                          "دليل المستخدم:\n\n"
                          "1. إنشاء جلسة جديدة\n"
                          "2. إدخال الهدف\n"
                          "3. اختيار نوع العملية\n"
                          "4. بدء التنفيذ\n"
                          "5. مراجعة النتائج\n\n"
                          "تأكد من وجود تصريح قانوني قبل الاستخدام!")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                          "Security Exploitation Framework\n"
                          "إطار عمل استغلال الأمان\n\n"
                          "الإصدار: 1.0.0\n"
                          "للاستخدام التعليمي والاختبارات المصرح بها فقط\n\n"
                          "تطوير: فريق الأمان السيبراني")
    
    def run(self):
        """تشغيل الواجهة الرسومية"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = SecurityFrameworkGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")

if __name__ == "__main__":
    main()
