# 🔐 **Security Exploitation Modules**
## وحدات الأمان والاختراق

هذا المجلد يحتوي على وحدات اختبار الأمان والاختراق التعليمية.

---

## 📋 **الوحدات المتاحة:**

### **1. Password Cracking (`password_cracking.py`)**
- **الوصف**: تقنيات كسر كلمات المرور المتعددة
- **الميزات**: 
  - Brute Force attacks
  - Dictionary attacks
  - Credential stuffing
  - Spear phishing
- **الاستخدام**: `python password_cracking.py`

### **2. Realistic Password Cracking (`realistic_password_cracking.py`)**
- **الوصف**: تقنيات كسر كلمات المرور الواقعية
- **الميزات**:
  - Social media password attacks
  - Realistic attack scenarios
  - Advanced cracking techniques
- **الاستخدام**: `python realistic_password_cracking.py`

### **3. Web Exploitation XSS (`web_exploitation_xss.py`)**
- **الوصف**: إطار عمل تعليمي لاستغلال ثغرات XSS
- **الميزات**:
  - Reflected XSS
  - Stored XSS
  - DOM-based XSS
  - Blind XSS
  - Educational test environment
- **الاستخدام**: `python web_exploitation_xss.py`

### **4. Info Stealer Educational (`info_stealer_educational.py`)**
- **الوصف**: إطار عمل تعليمي لسرقة المعلومات
- **الميزات**:
  - RedLine Stealer simulation
  - AZORult Stealer simulation
  - Emotet behavior simulation
  - Browser data extraction
  - Cryptocurrency wallet detection
- **الاستخدام**: `python info_stealer_educational.py`

---

## 🚀 **كيفية الاستخدام:**

### **استيراد الوحدات:**
```python
# استيراد وحدة محددة
from modules.security_exploitation.password_cracking import PasswordCracking

# استيراد جميع الوحدات
from modules.security_exploitation import *
```

### **تشغيل الوحدات:**
```bash
# من مجلد المشروع الرئيسي
cd /home/<USER>/Desktop/Year3/botnet/botnet_lab

# تشغيل وحدة XSS
python modules/security_exploitation/web_exploitation_xss.py

# تشغيل وحدة Info Stealer
python modules/security_exploitation/info_stealer_educational.py
```

---

## ⚠️ **تحذيرات مهمة:**

- 🎓 **للأغراض التعليمية فقط**
- ✅ **اختبر فقط على الأنظمة التي تملكها**
- ❌ **لا تستخدم على أنظمة غير مصرح بها**
- 🛡️ **استخدم هذه المعرفة للدفاع وتحسين الأمان**

---

## 📚 **مصادر إضافية:**

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [XSS Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html)
- [Password Security Guidelines](https://pages.nist.gov/800-63-3/sp800-63b.html)

---

**📞 للدعم**: راجع الوثائق في مجلد `docs/` أو ملفات README الأخرى.
