#!/usr/bin/env python3
"""
Test script for Predictive Analytics Module
سكربت اختبار لوحدة التحليلات التنبؤية

This script tests the real predictive analytics capabilities.
هذا السكربت يختبر قدرات التحليلات التنبؤية الحقيقية.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from predictive_analytics import RealPredictiveAnalytics
except ImportError as e:
    print(f"[-] Error importing predictive_analytics module: {e}")
    sys.exit(1)


def test_data_collection():
    """Test data collection capabilities"""
    print("\n" + "="*50)
    print("📊 Testing Data Collection")
    print("اختبار جمع البيانات")
    print("="*50)
    
    try:
        analytics = RealPredictiveAnalytics(output_dir="test_analytics_output")
        
        # Test system metrics collection
        print("\n[*] Testing system metrics collection...")
        system_metrics = analytics.collect_system_metrics()
        
        if system_metrics:
            print(f"✅ System metrics collected successfully!")
            print(f"   CPU: {system_metrics.get('cpu_percent', 0):.2f}%")
            print(f"   Memory: {system_metrics.get('memory_percent', 0):.2f}%")
            print(f"   Processes: {system_metrics.get('process_count', 0)}")
        
        # Test network data collection
        print("\n[*] Testing network data collection...")
        network_data = analytics.collect_network_data()
        
        if network_data:
            print(f"✅ Network data collected successfully!")
            print(f"   Connections: {network_data.get('connections_count', 0)}")
            print(f"   Bytes Sent: {network_data.get('bytes_sent', 0)}")
            print(f"   Network Speed: {network_data.get('network_speed', 0)}")
        
        # Test user behavior collection
        print("\n[*] Testing user behavior collection...")
        user_behavior = analytics.collect_user_behavior()
        
        if user_behavior:
            print(f"✅ User behavior collected successfully!")
            print(f"   Active Window: {user_behavior.get('active_window', 'Unknown')}")
            print(f"   Activity Score: {user_behavior.get('user_activity_score', 0):.2f}")
            print(f"   Idle Time: {user_behavior.get('idle_time', 0):.2f}s")
        
        # Test security events collection
        print("\n[*] Testing security events collection...")
        security_events = analytics.collect_security_events()
        
        if security_events:
            print(f"✅ Found {len(security_events)} security events")
            for event in security_events[:3]:
                print(f"   🔍 {event.get('event_type', 'unknown')}: {event.get('severity_level', 'unknown')}")
        else:
            print("ℹ️  No security events detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_model_training():
    """Test model training capabilities"""
    print("\n" + "="*50)
    print("🧠 Testing Model Training")
    print("اختبار تدريب النماذج")
    print("="*50)
    
    try:
        analytics = RealPredictiveAnalytics(output_dir="test_analytics_output")
        
        # Generate training data
        print("\n[*] Generating training data...")
        for i in range(25):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            if i % 5 == 0:
                analytics.collect_security_events()
            time.sleep(0.1)
        
        print(f"   Generated {len(analytics.system_metrics)} system metrics")
        print(f"   Generated {len(analytics.network_data)} network data points")
        print(f"   Generated {len(analytics.user_behavior)} user behavior points")
        print(f"   Generated {len(analytics.security_events)} security events")
        
        # Train models
        print("\n[*] Training prediction models...")
        training_success = analytics.train_models()
        
        if training_success:
            print("✅ Model training completed successfully!")
            
            # Check which models were trained
            trained_models = [name for name, model in analytics.models.items() if model is not None]
            print(f"   Trained models: {', '.join(trained_models)}")
        else:
            print("ℹ️  Model training skipped (may need more data or ML libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_predictions():
    """Test prediction capabilities"""
    print("\n" + "="*50)
    print("🔮 Testing Predictions")
    print("اختبار التنبؤات")
    print("="*50)
    
    try:
        analytics = RealPredictiveAnalytics(output_dir="test_analytics_output")
        
        # Generate data for predictions
        print("\n[*] Generating data for predictions...")
        for i in range(15):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            analytics.collect_security_events()
            time.sleep(0.1)
        
        # Train models first
        analytics.train_models()
        
        # Test system behavior prediction
        print("\n[*] Testing system behavior prediction...")
        system_pred = analytics.predict_system_behavior()
        
        if system_pred:
            print(f"✅ System behavior prediction:")
            print(f"   Target: {system_pred.get('prediction_target', 'unknown')}")
            print(f"   Value: {system_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {system_pred.get('confidence_score', 0):.2f}")
        
        # Test user activity prediction
        print("\n[*] Testing user activity prediction...")
        user_pred = analytics.predict_user_activity()
        
        if user_pred:
            print(f"✅ User activity prediction:")
            print(f"   Target: {user_pred.get('prediction_target', 'unknown')}")
            print(f"   Value: {user_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {user_pred.get('confidence_score', 0):.2f}")
        
        # Test network traffic prediction
        print("\n[*] Testing network traffic prediction...")
        network_pred = analytics.predict_network_traffic()
        
        if network_pred:
            print(f"✅ Network traffic prediction:")
            print(f"   Target: {network_pred.get('prediction_target', 'unknown')}")
            print(f"   Value: {network_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {network_pred.get('confidence_score', 0):.2f}")
        
        # Test threat prediction
        print("\n[*] Testing threat prediction...")
        threat_pred = analytics.create_threat_prediction()
        
        if threat_pred:
            print(f"✅ Threat prediction:")
            print(f"   Target: {threat_pred.get('prediction_target', 'unknown')}")
            print(f"   Value: {threat_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {threat_pred.get('confidence_score', 0):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_alerts_and_recommendations():
    """Test alerts and recommendations"""
    print("\n" + "="*50)
    print("🚨 Testing Alerts and Recommendations")
    print("اختبار التنبيهات والتوصيات")
    print("="*50)
    
    try:
        analytics = RealPredictiveAnalytics(output_dir="test_analytics_output")
        
        # Create mock predictions for testing
        mock_predictions = [
            {
                'prediction_type': 'system_behavior',
                'prediction_target': 'cpu_usage',
                'predicted_value': '85.5',
                'confidence_score': 0.8,
                'model_used': 'RandomForestRegressor',
                'prediction_horizon': 5,
                'timestamp': '2024-01-01T12:00:00'
            },
            {
                'prediction_type': 'user_activity',
                'prediction_target': 'activity_level',
                'predicted_value': 'active',
                'confidence_score': 0.9,
                'model_used': 'LogisticRegression',
                'prediction_horizon': 10,
                'timestamp': '2024-01-01T12:01:00'
            },
            {
                'prediction_type': 'threat_prediction',
                'prediction_target': 'threat_level',
                'predicted_value': 'high',
                'confidence_score': 0.85,
                'model_used': 'rule_based',
                'prediction_horizon': 15,
                'timestamp': '2024-01-01T12:02:00'
            }
        ]
        
        print("\n[*] Testing alert generation...")
        alerts = []
        for prediction in mock_predictions:
            alert = analytics.generate_alert(prediction)
            if alert:
                alerts.append(alert)
                print(f"✅ Alert generated:")
                print(f"   Type: {alert.get('alert_type', 'unknown')}")
                print(f"   Level: {alert.get('alert_level', 'unknown')}")
                print(f"   Message: {alert.get('alert_message', 'N/A')}")
                print()
        
        print(f"Total alerts generated: {len(alerts)}")
        
        # Test recommendations
        print("\n[*] Testing recommendations generation...")
        recommendations = analytics.generate_recommendations(mock_predictions)
        
        if recommendations:
            print(f"✅ Generated {len(recommendations)} recommendations:")
            for rec in recommendations:
                print(f"   📋 Type: {rec.get('type', 'unknown')}")
                print(f"      Action: {rec.get('action', 'N/A')}")
                print(f"      Reason: {rec.get('reason', 'N/A')}")
                print(f"      Priority: {rec.get('priority', 'unknown')}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_comprehensive_analysis():
    """Test comprehensive predictive analysis"""
    print("\n" + "="*50)
    print("🎯 Testing Comprehensive Analysis")
    print("اختبار التحليل الشامل")
    print("="*50)
    
    try:
        analytics = RealPredictiveAnalytics(output_dir="test_analytics_output")
        
        # Generate comprehensive data
        print("\n[*] Generating comprehensive data...")
        for i in range(20):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            if i % 3 == 0:
                analytics.collect_security_events()
            time.sleep(0.1)
        
        # Train models
        analytics.train_models()
        
        # Run comprehensive analysis
        print("\n[*] Running comprehensive predictive analysis...")
        results = analytics.run_predictive_analysis()
        
        if results:
            print(f"✅ Comprehensive analysis completed!")
            print(f"   Timestamp: {results.get('timestamp', 'N/A')}")
            print(f"   Predictions: {len(results.get('predictions', []))}")
            print(f"   Alerts: {len(results.get('alerts', []))}")
            print(f"   Recommendations: {len(results.get('recommendations', []))}")
            
            # Show summary of predictions
            predictions = results.get('predictions', [])
            if predictions:
                print(f"\n📊 Prediction Summary:")
                for pred in predictions:
                    print(f"   🔮 {pred.get('prediction_type', 'unknown')}: {pred.get('predicted_value', 'N/A')}")
            
            # Show alerts
            alerts = results.get('alerts', [])
            if alerts:
                print(f"\n🚨 Alert Summary:")
                for alert in alerts:
                    print(f"   ⚠️  {alert.get('alert_level', 'unknown').upper()}: {alert.get('alert_message', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Predictive Analytics Module Test Suite")
    print("مجموعة اختبارات وحدة التحليلات التنبؤية")
    print("="*60)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*60)
    
    # Run tests
    tests = [
        ("Data Collection", test_data_collection),
        ("Model Training", test_model_training),
        ("Predictions", test_predictions),
        ("Alerts and Recommendations", test_alerts_and_recommendations),
        ("Comprehensive Analysis", test_comprehensive_analysis)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[-] {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("ملخص نتائج الاختبارات")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Predictive Analytics module is working correctly.")
        print("🎉 جميع الاختبارات نجحت! وحدة التحليلات التنبؤية تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Check the errors above.")
        print(f"⚠️  {total-passed} اختبار فشل. تحقق من الأخطاء أعلاه.")
    
    print(f"\n📁 Test output saved to: test_analytics_output/")
    print(f"📁 مخرجات الاختبار محفوظة في: test_analytics_output/")
    
    return results


if __name__ == "__main__":
    main()
