# Security Exploitation Framework .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Security Framework specific files
# Sensitive data and keys
data/keys/
*.key
*.pem
*.p12
*.pfx

# Database files
data/databases/*.db
data/databases/*.sqlite
data/databases/*.sqlite3

# Log files
logs/
*.log

# Cache files
data/cache/
*.cache

# Configuration files with sensitive data
config.local.yaml
config.production.yaml
.env.local
.env.production

# API keys and secrets
api_keys.txt
secrets.txt
credentials.json

# Reports and output
reports/
output/
results/

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup
*~

# Compiled binaries
*.exe
*.dll
*.so
*.dylib

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Documentation build
docs/_build/
docs/build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Security tools output
nmap_output/
masscan_output/
nuclei_output/
subfinder_output/

# Wordlists (large files)
data/wordlists/*.txt
data/wordlists/*.lst

# Exploit databases
exploits/private/
exploits/zero_day/

# Payloads
payloads/custom/
payloads/private/

# Virtual lab configurations
lab_configs/
vm_configs/

# SSL certificates
*.crt
*.cert
*.ca-bundle

# SSH keys
id_rsa
id_rsa.pub
id_ed25519
id_ed25519.pub

# GPG keys
*.gpg
*.asc

# Docker
.dockerignore
Dockerfile.local

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry

# Local development
local_settings.py
local_config.yaml

# Test data
test_data/
sample_data/

# Performance profiling
*.prof

# Memory dumps
*.dump
*.dmp

# Core dumps
core.*

# Malware samples (should never be in git)
malware_samples/
*.malware
*.virus

# Network captures
*.pcap
*.pcapng
*.cap

# Forensic images
*.dd
*.img
*.e01

# Large datasets
datasets/
training_data/

# Model files
models/
*.model
*.pkl
*.joblib

# Temporary analysis files
analysis_temp/
temp_analysis/

# Setup logs
setup.log
install.log

# Runtime files
*.pid
*.sock

# Lock files
*.lock

# Editor temporary files
.#*
\#*#

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
*~
tags
[._]*.un~
