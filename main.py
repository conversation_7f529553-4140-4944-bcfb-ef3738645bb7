#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الملف الرئيسي لإطار عمل استغلال الأمان
Security Exploitation Framework Main Entry Point
"""

import sys
import os
import argparse
import logging
from pathlib import Path
import json
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent))

from core.config_manager import config
from core.database_manager import db_manager
from core.security_manager import security_manager
from core.logger import security_logger
from core.real_data_generator import RealDataGenerator
from modules.reconnaissance.basic_recon import BasicReconModule

class SecurityFramework:
    """الفئة الرئيسية لإطار عمل الأمان"""
    
    def __init__(self):
        """تهيئة الإطار"""
        self.logger = logging.getLogger(__name__)
        self.config_loaded = False
        self.session_id = None
        self.modules = {}
        
        # تسجيل بدء التشغيل
        security_logger.log_security_event(
            "framework_startup",
            {"timestamp": datetime.now().isoformat()},
            level="INFO"
        )
    
    def initialize(self, config_path: str = "config.yaml") -> bool:
        """
        تهيئة الإطار
        
        Args:
            config_path: مسار ملف الإعدادات
            
        Returns:
            bool: True إذا تم التهيئة بنجاح
        """
        try:
            # تحميل الإعدادات
            if not config.load_config(config_path):
                print(f"❌ Failed to load configuration from {config_path}")
                return False
            
            self.config_loaded = True
            
            # إعداد التسجيل
            log_config = config.get_section("logging")
            security_logger.configure_from_dict(log_config)
            
            # التحقق من صحة الإعدادات
            validation = config.validate_config()
            if not validation["valid"]:
                print("⚠️ Configuration validation failed:")
                for error in validation["errors"]:
                    print(f"   - {error}")
                return False
            
            if validation["warnings"]:
                print("⚠️ Configuration warnings:")
                for warning in validation["warnings"]:
                    print(f"   - {warning}")
            
            # تهيئة الوحدات
            self._initialize_modules()
            
            print("✅ Security Exploitation Framework initialized successfully")
            self.logger.info("Framework initialized successfully")
            
            return True
            
        except Exception as e:
            print(f"❌ Framework initialization failed: {e}")
            self.logger.error(f"Framework initialization failed: {e}")
            return False
    
    def _initialize_modules(self):
        """تهيئة الوحدات المتاحة"""
        try:
            # وحدة الاستطلاع الأساسية
            recon_config = config.get_section("modules.reconnaissance")
            if recon_config.get("enabled", True):
                self.modules["basic_recon"] = BasicReconModule(recon_config)
                self.logger.info("Basic reconnaissance module loaded")
            
            self.logger.info(f"Loaded {len(self.modules)} modules")
            
        except Exception as e:
            self.logger.error(f"Error initializing modules: {e}")
    
    def create_session(self, user_id: str = "default_user") -> str:
        """
        إنشاء جلسة جديدة
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            str: معرف الجلسة
        """
        try:
            self.session_id = db_manager.create_session(user_id)
            
            # تسجيل إنشاء الجلسة
            security_logger.log_security_event(
                "session_created",
                {"user_id": user_id, "session_id": self.session_id},
                level="INFO",
                user_id=user_id,
                session_id=self.session_id
            )
            
            self.logger.info(f"Session created: {self.session_id}")
            return self.session_id
            
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            return None
    
    def run_reconnaissance(self, target: str, modules: list = None) -> dict:
        """
        تشغيل وحدات الاستطلاع
        
        Args:
            target: الهدف
            modules: قائمة الوحدات (اختياري)
            
        Returns:
            dict: نتائج الاستطلاع
        """
        if not self.session_id:
            self.create_session()
        
        results = {
            "target": target,
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "modules_results": {}
        }
        
        # تحديد الوحدات المراد تشغيلها
        if modules is None:
            modules = ["basic_recon"]
        
        for module_name in modules:
            if module_name in self.modules:
                try:
                    module = self.modules[module_name]
                    
                    # تعيين الجلسة
                    module.set_session(self.session_id)
                    
                    # تعيين التصريح (يجب الحصول على تصريح حقيقي في البيئة الإنتاجية)
                    module.set_authorization(True, True)
                    
                    # تنفيذ الوحدة
                    print(f"🔍 Running {module_name} on {target}...")
                    module_result = module.start_execution(target)
                    
                    results["modules_results"][module_name] = module_result
                    
                    # حفظ النتائج في قاعدة البيانات
                    db_manager.save_module_result(
                        self.session_id,
                        module_name,
                        module.module_type.value,
                        target,
                        module_result
                    )
                    
                    print(f"✅ {module_name} completed")
                    
                except Exception as e:
                    error_msg = f"Module {module_name} failed: {e}"
                    results["modules_results"][module_name] = {
                        "success": False,
                        "error": str(e)
                    }
                    print(f"❌ {error_msg}")
                    self.logger.error(error_msg)
            else:
                print(f"⚠️ Module {module_name} not found")
        
        return results
    
    def generate_real_data(self, data_type: str = "vulnerabilities", **kwargs) -> dict:
        """
        توليد بيانات حقيقية
        
        Args:
            data_type: نوع البيانات
            **kwargs: معاملات إضافية
            
        Returns:
            dict: البيانات المولدة
        """
        try:
            data_generator = RealDataGenerator(config.get_section("real_data"))
            
            if data_type == "vulnerabilities":
                days_back = kwargs.get("days_back", 7)
                print(f"📊 Fetching real vulnerabilities from last {days_back} days...")
                data = data_generator.fetch_real_vulnerabilities(days_back=days_back)
                
                # حفظ في قاعدة البيانات
                for vuln in data:
                    db_manager.store_vulnerability(vuln)
                
                print(f"✅ Fetched {len(data)} vulnerabilities")
                
            elif data_type == "network_traffic":
                duration = kwargs.get("duration_hours", 24)
                print(f"📊 Generating network traffic data for {duration} hours...")
                data = data_generator.generate_realistic_network_traffic(duration_hours=duration)
                print(f"✅ Generated {len(data)} traffic entries")
                
            elif data_type == "user_behavior":
                num_users = kwargs.get("num_users", 50)
                days = kwargs.get("days", 7)
                print(f"📊 Generating user behavior data for {num_users} users over {days} days...")
                data = data_generator.create_realistic_user_behavior(num_users=num_users, days=days)
                print(f"✅ Generated behavior data for {len(data)} users")
                
            elif data_type == "attack_scenarios":
                print("📊 Generating attack scenarios...")
                data = data_generator.simulate_attack_scenarios()
                print(f"✅ Generated {len(data)} attack scenarios")
                
            else:
                raise ValueError(f"Unknown data type: {data_type}")
            
            return {
                "success": True,
                "data_type": data_type,
                "count": len(data) if isinstance(data, list) else 1,
                "data": data
            }
            
        except Exception as e:
            error_msg = f"Failed to generate {data_type}: {e}"
            print(f"❌ {error_msg}")
            self.logger.error(error_msg)
            return {
                "success": False,
                "error": str(e)
            }
    
    def show_statistics(self):
        """عرض إحصائيات النظام"""
        print("\n" + "="*60)
        print("📊 Security Framework Statistics")
        print("="*60)
        
        # إحصائيات قاعدة البيانات
        db_stats = db_manager.get_database_statistics()
        print("\n🗄️ Database Statistics:")
        for key, value in db_stats.items():
            print(f"   {key}: {value}")
        
        # إحصائيات الأمان
        security_stats = security_manager.get_security_statistics()
        print("\n🔐 Security Statistics:")
        for key, value in security_stats.items():
            print(f"   {key}: {value}")
        
        # إحصائيات الوحدات
        print(f"\n🔧 Modules Statistics:")
        print(f"   Loaded modules: {len(self.modules)}")
        for module_name in self.modules:
            print(f"   - {module_name}")
        
        print("\n" + "="*60)
    
    def cleanup(self):
        """تنظيف الموارد"""
        if self.session_id:
            db_manager.close_session(self.session_id)
            
        security_logger.log_security_event(
            "framework_shutdown",
            {"timestamp": datetime.now().isoformat()},
            level="INFO",
            session_id=self.session_id
        )
        
        print("🧹 Framework cleanup completed")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="Security Exploitation Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --recon example.com
  python main.py --generate-data vulnerabilities --days-back 30
  python main.py --generate-data network_traffic --duration-hours 48
  python main.py --stats
        """
    )
    
    parser.add_argument("--config", default="config.yaml", 
                       help="Configuration file path")
    parser.add_argument("--recon", metavar="TARGET",
                       help="Run reconnaissance on target")
    parser.add_argument("--generate-data", choices=["vulnerabilities", "network_traffic", "user_behavior", "attack_scenarios"],
                       help="Generate real data")
    parser.add_argument("--days-back", type=int, default=7,
                       help="Days back for vulnerability data")
    parser.add_argument("--duration-hours", type=int, default=24,
                       help="Duration in hours for network traffic")
    parser.add_argument("--num-users", type=int, default=50,
                       help="Number of users for behavior data")
    parser.add_argument("--days", type=int, default=7,
                       help="Number of days for user behavior")
    parser.add_argument("--stats", action="store_true",
                       help="Show system statistics")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    # إعداد التسجيل
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # إنشاء الإطار
    framework = SecurityFramework()
    
    try:
        # تهيئة الإطار
        if not framework.initialize(args.config):
            sys.exit(1)
        
        # تنفيذ الأوامر
        if args.recon:
            print(f"\n🎯 Starting reconnaissance on: {args.recon}")
            results = framework.run_reconnaissance(args.recon)
            
            # عرض النتائج
            print(f"\n📋 Reconnaissance Results:")
            print(json.dumps(results, indent=2, ensure_ascii=False))
            
        elif args.generate_data:
            kwargs = {}
            if args.generate_data == "vulnerabilities":
                kwargs["days_back"] = args.days_back
            elif args.generate_data == "network_traffic":
                kwargs["duration_hours"] = args.duration_hours
            elif args.generate_data == "user_behavior":
                kwargs["num_users"] = args.num_users
                kwargs["days"] = args.days
            
            result = framework.generate_real_data(args.generate_data, **kwargs)
            
            if result["success"]:
                print(f"\n📊 Data Generation Results:")
                print(f"   Type: {result['data_type']}")
                print(f"   Count: {result['count']}")
            
        elif args.stats:
            framework.show_statistics()
            
        else:
            print("🎯 Security Exploitation Framework")
            print("Use --help for available commands")
            framework.show_statistics()
    
    except KeyboardInterrupt:
        print("\n⚠️ Operation interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logging.exception("Unexpected error occurred")
    finally:
        framework.cleanup()

if __name__ == "__main__":
    main()
