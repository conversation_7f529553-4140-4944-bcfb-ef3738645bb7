# Security Exploitation Modules Requirements
# متطلبات وحدات استغلال الأمان

# Core system monitoring and process management
psutil>=5.9.0

# Windows-specific libraries for credential decryption and system access
pywin32>=306; sys_platform == "win32"

# Database support (usually included with Python)
# sqlite3 is part of Python standard library

# Machine Learning and AI (for Advanced Intelligence & Predictive Analytics)
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Deep Learning (for LSTM and advanced models)
tensorflow>=2.8.0

# Time Series Analysis (for Predictive Analytics)
statsmodels>=0.13.0

# Natural Language Processing
nltk>=3.7
textblob>=0.17.1

# Computer Vision
opencv-python>=4.5.0
pytesseract>=0.3.8
Pillow>=8.3.0

# File system monitoring
watchdog>=2.1.0

# Network Security and Penetration Testing (for Advanced Propagation)
paramiko>=2.9.0
python-nmap>=0.7.1
impacket>=0.10.0
scapy>=2.4.5

# Data Visualization and Analysis
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Network and Web Libraries
requests>=2.28.0
urllib3>=1.26.0
dnspython>=2.2.0

# Cryptography and Security
cryptography>=3.4.8
pycryptodome>=3.15.0

# Utility Libraries
joblib>=1.1.0
tqdm>=4.64.0

# Network Security and Penetration Testing (for Advanced Propagation)
paramiko>=2.9.0
python-nmap>=0.7.1
impacket>=0.10.0
scapy>=2.4.5

# Network and Web Libraries
urllib3>=1.26.0
dnspython>=2.2.0

# Cryptography and Security
pycryptodome>=3.15.0

# Utility Libraries
tqdm>=4.64.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0

# Optional: Advanced time series and forecasting
prophet>=1.1.0
xgboost>=1.6.0

# Optional: Advanced network analysis
netaddr>=0.8.0


