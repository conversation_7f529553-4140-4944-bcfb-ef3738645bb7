# Core requirements with real data support
psutil>=5.9.0              # مراقبة النظام
pyyaml>=6.0                # معالجة YAML
sqlalchemy>=1.4.0          # ORM قاعدة البيانات
cryptography>=3.4.8       # التشفير
requests>=2.28.0           # HTTP requests
asyncio>=3.4.0             # البرمجة غير المتزامنة

# Real data collection and processing
beautifulsoup4>=4.11.0     # تحليل HTML للـ web scraping
lxml>=4.9.0                # معالجة XML/HTML سريعة
feedparser>=6.0.0          # تحليل RSS/Atom feeds
python-whois>=0.8.0        # معلومات WHOIS
dnspython>=2.2.0           # استعلامات DNS
geoip2>=4.6.0              # تحديد الموقع الجغرافي
maxminddb>=2.2.0           # قاعدة بيانات GeoIP

# Real threat intelligence and OSINT
shodan>=1.28.0             # Shodan API للبحث في الأجهزة
censys>=2.1.0              # Censys API للبحث في الشهادات
virustotal-api>=1.1.11     # VirusTotal API
haveibeenpwned>=3.0.0      # Have I Been Pwned API
threatcrowd>=1.0.0         # ThreatCrowd API

# Network data collection
scapy>=2.4.5               # تحليل حزم الشبكة
pyshark>=0.5.3             # تحليل pcap files
netaddr>=0.8.0             # معالجة عناوين الشبكة
ipaddress>=1.0.23          # معالجة عناوين IP
python-nmap>=0.7.1         # فحص الشبكة

# Log analysis and parsing
python-logstash>=0.4.8     # تحليل سجلات Logstash
elasticsearch>=8.0.0       # البحث في السجلات
apache-log-parser>=1.7.0   # تحليل سجلات Apache

# Virtual environment management
pyvmomi>=7.0.3             # VMware vSphere API
libvirt-python>=8.0.0      # KVM/QEMU management
docker>=6.0.0              # Docker containers

# Infrastructure as Code
ansible>=6.0.0             # أتمتة النشر

# Monitoring and logging
prometheus-client>=0.14.0  # مراقبة الأداء

# AI/ML requirements
tensorflow>=2.8.0          # التعلم العميق
scikit-learn>=1.0.0        # التعلم الآلي
pandas>=1.3.0              # معالجة البيانات
numpy>=1.21.0              # العمليات الرياضية
matplotlib>=3.5.0          # الرسوم البيانية

# Network & Security requirements
paramiko>=2.9.0            # SSH
impacket>=0.10.0           # بروتوكولات الشبكة

# GUI requirements
flask>=2.0.0               # واجهة ويب
fastapi>=0.70.0            # API سريع
websockets>=10.0           # اتصالات WebSocket

# Database requirements
redis>=6.0                 # تخزين مؤقت

# Additional utilities
colorama>=0.4.4            # ألوان في Terminal
rich>=12.0.0               # تنسيق النصوص
click>=8.0.0               # واجهة سطر الأوامر
tqdm>=4.64.0               # شريط التقدم
python-dateutil>=2.8.0     # معالجة التواريخ
validators>=0.20.0         # التحقق من صحة البيانات
