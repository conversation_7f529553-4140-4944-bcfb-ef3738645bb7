#!/usr/bin/env python3
"""
Demo script for Intelligence Gathering Module
سكربت تجريبي لوحدة جمع المعلومات الاستخباراتية

This script demonstrates the basic functionality of the intelligence gathering module.
هذا السكربت يوضح الوظائف الأساسية لوحدة جمع المعلومات الاستخباراتية.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_basic_system_info():
    """Demo basic system information gathering"""
    print("\n" + "="*50)
    print("🖥️  Basic System Information Demo")
    print("عرض توضيحي لمعلومات النظام الأساسية")
    print("="*50)
    
    try:
        from intelligence_gathering import RealIntelligenceGatherer
        
        # Initialize gatherer
        gatherer = RealIntelligenceGatherer(output_dir="demo_output")
        
        # Get basic system info
        print("\n[*] Collecting basic system information...")
        basic_info = gatherer.get_basic_system_info()
        
        if basic_info:
            print(f"✅ System information collected successfully!")
            print(f"   Hostname: {basic_info.get('hostname', 'Unknown')}")
            print(f"   OS: {basic_info.get('os_name', 'Unknown')} {basic_info.get('os_version', '')}")
            print(f"   Architecture: {basic_info.get('architecture', 'Unknown')}")
            print(f"   Processor: {basic_info.get('processor', 'Unknown')}")
            
            if 'memory_total' in basic_info:
                memory_gb = basic_info['memory_total'] / (1024**3)
                print(f"   Memory: {memory_gb:.2f} GB")
            
            return True
        else:
            print("❌ Failed to collect system information")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure the intelligence_gathering module is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_network_info():
    """Demo network information gathering"""
    print("\n" + "="*50)
    print("🌐 Network Information Demo")
    print("عرض توضيحي لمعلومات الشبكة")
    print("="*50)
    
    try:
        from intelligence_gathering import RealIntelligenceGatherer
        
        gatherer = RealIntelligenceGatherer(output_dir="demo_output")
        
        # Get network interfaces
        print("\n[*] Collecting network interface information...")
        interfaces = gatherer.get_network_interfaces_detailed()
        
        if interfaces:
            print(f"✅ Found {len(interfaces)} network interfaces:")
            for interface_name, interface_info in interfaces.items():
                print(f"   📡 {interface_name}")
                addresses = interface_info.get('addresses', [])
                for addr in addresses[:2]:  # Show first 2 addresses
                    if addr.get('address') and not addr['address'].startswith('fe80'):
                        print(f"      - {addr.get('address', 'N/A')}")
        
        # Get active connections (limited)
        print("\n[*] Collecting active network connections...")
        connections = gatherer.get_active_connections()
        
        if connections:
            print(f"✅ Found {len(connections)} active connections")
            # Show first few connections
            for conn in connections[:5]:
                local = conn.get('local_address', 'N/A')
                remote = conn.get('remote_address', 'N/A')
                status = conn.get('status', 'N/A')
                print(f"   🔗 {local} -> {remote} ({status})")
            
            if len(connections) > 5:
                print(f"   ... and {len(connections) - 5} more connections")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_security_detection():
    """Demo security software detection"""
    print("\n" + "="*50)
    print("🛡️  Security Software Detection Demo")
    print("عرض توضيحي لاكتشاف برامج الأمان")
    print("="*50)
    
    try:
        from intelligence_gathering import RealIntelligenceGatherer
        
        gatherer = RealIntelligenceGatherer(output_dir="demo_output")
        
        # Detect antivirus
        print("\n[*] Detecting antivirus software...")
        antivirus = gatherer.detect_antivirus()
        
        if antivirus:
            print(f"✅ Found {len(antivirus)} antivirus processes:")
            for av in antivirus:
                print(f"   🦠 {av.get('name', 'Unknown')} (PID: {av.get('pid', 'N/A')})")
        else:
            print("ℹ️  No antivirus processes detected")
        
        # Detect security software
        print("\n[*] Detecting security software...")
        security_software = gatherer.detect_security_software()
        
        if security_software:
            print(f"✅ Found {len(security_software)} security-related processes:")
            for sec in security_software[:5]:  # Show first 5
                print(f"   🔒 {sec.get('name', 'Unknown')} (Keyword: {sec.get('keyword_matched', 'N/A')})")
            
            if len(security_software) > 5:
                print(f"   ... and {len(security_software) - 5} more security processes")
        else:
            print("ℹ️  No security software detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_process_enumeration():
    """Demo process enumeration"""
    print("\n" + "="*50)
    print("⚙️  Process Enumeration Demo")
    print("عرض توضيحي لتعداد العمليات")
    print("="*50)
    
    try:
        from intelligence_gathering import RealIntelligenceGatherer
        
        gatherer = RealIntelligenceGatherer(output_dir="demo_output")
        
        print("\n[*] Enumerating running processes...")
        processes = gatherer.get_running_processes()
        
        if processes:
            print(f"✅ Found {len(processes)} running processes")
            
            # Show some interesting processes
            interesting_processes = []
            for proc in processes:
                proc_name = proc.get('name', '').lower()
                if any(keyword in proc_name for keyword in ['chrome', 'firefox', 'notepad', 'explorer', 'cmd', 'powershell']):
                    interesting_processes.append(proc)
            
            if interesting_processes:
                print(f"📋 Some interesting processes:")
                for proc in interesting_processes[:10]:  # Show first 10
                    name = proc.get('name', 'Unknown')
                    pid = proc.get('pid', 'N/A')
                    username = proc.get('username', 'N/A')
                    print(f"   🔧 {name} (PID: {pid}, User: {username})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main demo function"""
    print("🎯 Intelligence Gathering Module Demo")
    print("عرض توضيحي لوحدة جمع المعلومات الاستخباراتية")
    print("="*60)
    print("⚠️  This is a demonstration of authorized testing capabilities")
    print("⚠️  هذا عرض توضيحي لقدرات الاختبار المصرح بها")
    print("="*60)
    
    # Run demos
    demos = [
        ("Basic System Information", demo_basic_system_info),
        ("Network Information", demo_network_info),
        ("Security Detection", demo_security_detection),
        ("Process Enumeration", demo_process_enumeration)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n🔄 Running {demo_name} demo...")
        try:
            results[demo_name] = demo_func()
        except Exception as e:
            print(f"❌ {demo_name} demo failed: {e}")
            results[demo_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEMO RESULTS SUMMARY")
    print("ملخص نتائج العرض التوضيحي")
    print("="*60)
    
    passed = 0
    total = len(demos)
    
    for demo_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} demos successful")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All demos completed successfully!")
        print("🎉 جميع العروض التوضيحية اكتملت بنجاح!")
        print("\nThe intelligence gathering module is working correctly.")
        print("وحدة جمع المعلومات الاستخباراتية تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} demo(s) failed.")
        print(f"⚠️  {total-passed} عرض توضيحي فشل.")
        print("\nCheck the error messages above for troubleshooting.")
        print("تحقق من رسائل الخطأ أعلاه لاستكشاف الأخطاء.")
    
    print(f"\n📁 Demo output saved to: demo_output/")
    print(f"📁 مخرجات العرض التوضيحي محفوظة في: demo_output/")

if __name__ == "__main__":
    main()
