#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة OSINT المتقدمة
Advanced OSINT Module with External API Integration
"""

import requests
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import base64
import hashlib
import re
from urllib.parse import urlparse
import socket

# إضافة مسار المشروع للاستيراد
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.base_module import BaseSecurityModule, ModuleType

class AdvancedOSINTModule(BaseSecurityModule):
    """وحدة OSINT المتقدمة مع تكامل APIs خارجية"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة وحدة OSINT المتقدمة
        
        Args:
            config: إعدادات الوحدة
        """
        super().__init__("advanced_osint", ModuleType.RECONNAISSANCE, config)
        
        # إعدادات APIs
        self.shodan_api_key = self.config.get('shodan_api_key', '')
        self.virustotal_api_key = self.config.get('virustotal_api_key', '')
        self.haveibeenpwned_api_key = self.config.get('haveibeenpwned_api_key', '')
        
        # URLs للـ APIs
        self.shodan_base_url = "https://api.shodan.io"
        self.virustotal_base_url = "https://www.virustotal.com/vtapi/v2"
        self.haveibeenpwned_base_url = "https://haveibeenpwned.com/api/v3"
        
        # إعدادات التأخير لتجنب Rate Limiting
        self.api_delay = self.config.get('api_delay', 1)
        
        self.logger.info("Advanced OSINT Module initialized")
    
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: الهدف (IP, domain, email, hash, etc.)
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        # يمكن أن يكون الهدف IP أو domain أو email أو hash
        if self._is_ip_address(target):
            return True
        elif self._is_domain(target):
            return True
        elif self._is_email(target):
            return True
        elif self._is_hash(target):
            return True
        return False
    
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ OSINT المتقدم
        
        Args:
            target: الهدف
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج OSINT المتقدم
        """
        try:
            self.log_activity("Starting advanced OSINT", data={"target": target})
            
            results = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": {}
            }
            
            # تحديد نوع الهدف
            target_type = self._identify_target_type(target)
            results["data"]["target_type"] = target_type
            
            self.update_progress(10, f"Target identified as: {target_type}")
            
            # تنفيذ العمليات حسب نوع الهدف
            if target_type == "ip":
                results["data"].update(self._analyze_ip(target))
            elif target_type == "domain":
                results["data"].update(self._analyze_domain(target))
            elif target_type == "email":
                results["data"].update(self._analyze_email(target))
            elif target_type == "hash":
                results["data"].update(self._analyze_hash(target))
            
            # تحليل شامل للنتائج
            self.update_progress(90, "Analyzing results...")
            analysis = self._analyze_osint_results(results["data"])
            results["data"]["analysis"] = analysis
            
            self.update_progress(100, "Advanced OSINT completed")
            
            self.log_activity("Advanced OSINT completed", 
                            data={"target": target, "findings": len(results["data"])})
            
            return results
            
        except Exception as e:
            self.add_error(f"Advanced OSINT failed: {str(e)}")
            return {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def _identify_target_type(self, target: str) -> str:
        """تحديد نوع الهدف"""
        if self._is_ip_address(target):
            return "ip"
        elif self._is_email(target):
            return "email"
        elif self._is_hash(target):
            return "hash"
        elif self._is_domain(target):
            return "domain"
        return "unknown"
    
    def _analyze_ip(self, ip: str) -> Dict[str, Any]:
        """تحليل عنوان IP"""
        results = {}
        
        # Shodan lookup
        self.update_progress(20, "Querying Shodan...")
        if self.shodan_api_key:
            shodan_data = self._shodan_ip_lookup(ip)
            if shodan_data:
                results["shodan"] = shodan_data
        
        # معلومات إضافية
        self.update_progress(40, "Gathering additional IP info...")
        results["geolocation"] = self._get_ip_geolocation(ip)
        results["reverse_dns"] = self._get_reverse_dns(ip)
        results["port_scan_history"] = self._get_port_scan_history(ip)
        
        return results
    
    def _analyze_domain(self, domain: str) -> Dict[str, Any]:
        """تحليل النطاق"""
        results = {}
        
        # Shodan domain lookup
        self.update_progress(30, "Querying Shodan for domain...")
        if self.shodan_api_key:
            shodan_data = self._shodan_domain_lookup(domain)
            if shodan_data:
                results["shodan"] = shodan_data
        
        # معلومات DNS متقدمة
        self.update_progress(50, "Advanced DNS analysis...")
        results["dns_advanced"] = self._advanced_dns_analysis(domain)
        results["subdomains"] = self._find_subdomains(domain)
        results["ssl_certificates"] = self._analyze_ssl_certificates(domain)
        
        return results
    
    def _analyze_email(self, email: str) -> Dict[str, Any]:
        """تحليل البريد الإلكتروني"""
        results = {}
        
        # Have I Been Pwned lookup
        self.update_progress(25, "Checking breach databases...")
        if self.haveibeenpwned_api_key:
            breach_data = self._haveibeenpwned_lookup(email)
            if breach_data:
                results["breaches"] = breach_data
        
        # تحليل النطاق المرتبط
        domain = email.split('@')[1] if '@' in email else None
        if domain:
            self.update_progress(60, "Analyzing associated domain...")
            results["domain_analysis"] = self._analyze_domain(domain)
        
        return results
    
    def _analyze_hash(self, hash_value: str) -> Dict[str, Any]:
        """تحليل الهاش"""
        results = {}
        
        # VirusTotal lookup
        self.update_progress(35, "Querying VirusTotal...")
        if self.virustotal_api_key:
            vt_data = self._virustotal_hash_lookup(hash_value)
            if vt_data:
                results["virustotal"] = vt_data
        
        # تحديد نوع الهاش
        results["hash_type"] = self._identify_hash_type(hash_value)
        
        return results
    
    def _shodan_ip_lookup(self, ip: str) -> Optional[Dict]:
        """البحث في Shodan عن IP"""
        try:
            url = f"{self.shodan_base_url}/shodan/host/{ip}"
            params = {"key": self.shodan_api_key}
            
            response = requests.get(url, params=params, timeout=30)
            time.sleep(self.api_delay)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "ip": data.get("ip_str"),
                    "hostnames": data.get("hostnames", []),
                    "country": data.get("country_name"),
                    "city": data.get("city"),
                    "org": data.get("org"),
                    "isp": data.get("isp"),
                    "ports": data.get("ports", []),
                    "services": [
                        {
                            "port": service.get("port"),
                            "protocol": service.get("transport"),
                            "product": service.get("product"),
                            "version": service.get("version"),
                            "banner": service.get("data", "")[:200]
                        }
                        for service in data.get("data", [])
                    ],
                    "vulnerabilities": data.get("vulns", []),
                    "last_update": data.get("last_update")
                }
            else:
                self.add_warning(f"Shodan API error: {response.status_code}")
                
        except Exception as e:
            self.add_warning(f"Shodan lookup failed: {e}")
        
        return None
    
    def _shodan_domain_lookup(self, domain: str) -> Optional[Dict]:
        """البحث في Shodan عن النطاق"""
        try:
            url = f"{self.shodan_base_url}/dns/domain/{domain}"
            params = {"key": self.shodan_api_key}
            
            response = requests.get(url, params=params, timeout=30)
            time.sleep(self.api_delay)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "domain": domain,
                    "subdomains": data.get("subdomains", []),
                    "more": data.get("more", False)
                }
            else:
                self.add_warning(f"Shodan domain API error: {response.status_code}")
                
        except Exception as e:
            self.add_warning(f"Shodan domain lookup failed: {e}")
        
        return None
    
    def _virustotal_hash_lookup(self, hash_value: str) -> Optional[Dict]:
        """البحث في VirusTotal عن الهاش"""
        try:
            url = f"{self.virustotal_base_url}/file/report"
            params = {
                "apikey": self.virustotal_api_key,
                "resource": hash_value
            }
            
            response = requests.get(url, params=params, timeout=30)
            time.sleep(self.api_delay)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("response_code") == 1:
                    return {
                        "hash": hash_value,
                        "scan_date": data.get("scan_date"),
                        "positives": data.get("positives", 0),
                        "total": data.get("total", 0),
                        "detection_ratio": f"{data.get('positives', 0)}/{data.get('total', 0)}",
                        "permalink": data.get("permalink"),
                        "scans": data.get("scans", {}),
                        "md5": data.get("md5"),
                        "sha1": data.get("sha1"),
                        "sha256": data.get("sha256")
                    }
                else:
                    return {"hash": hash_value, "found": False}
            else:
                self.add_warning(f"VirusTotal API error: {response.status_code}")
                
        except Exception as e:
            self.add_warning(f"VirusTotal lookup failed: {e}")
        
        return None
    
    def _haveibeenpwned_lookup(self, email: str) -> Optional[Dict]:
        """البحث في Have I Been Pwned"""
        try:
            url = f"{self.haveibeenpwned_base_url}/breachedaccount/{email}"
            headers = {
                "hibp-api-key": self.haveibeenpwned_api_key,
                "User-Agent": "Security Framework OSINT Module"
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            time.sleep(self.api_delay)
            
            if response.status_code == 200:
                breaches = response.json()
                return {
                    "email": email,
                    "breach_count": len(breaches),
                    "breaches": [
                        {
                            "name": breach.get("Name"),
                            "domain": breach.get("Domain"),
                            "breach_date": breach.get("BreachDate"),
                            "added_date": breach.get("AddedDate"),
                            "pwn_count": breach.get("PwnCount"),
                            "description": breach.get("Description"),
                            "data_classes": breach.get("DataClasses", [])
                        }
                        for breach in breaches
                    ]
                }
            elif response.status_code == 404:
                return {"email": email, "breach_count": 0, "breaches": []}
            else:
                self.add_warning(f"HaveIBeenPwned API error: {response.status_code}")
                
        except Exception as e:
            self.add_warning(f"HaveIBeenPwned lookup failed: {e}")
        
        return None
    
    def _get_ip_geolocation(self, ip: str) -> Dict[str, Any]:
        """الحصول على الموقع الجغرافي للـ IP"""
        try:
            # استخدام خدمة مجانية للموقع الجغرافي
            url = f"http://ip-api.com/json/{ip}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    return {
                        "country": data.get("country"),
                        "country_code": data.get("countryCode"),
                        "region": data.get("regionName"),
                        "city": data.get("city"),
                        "zip": data.get("zip"),
                        "lat": data.get("lat"),
                        "lon": data.get("lon"),
                        "timezone": data.get("timezone"),
                        "isp": data.get("isp"),
                        "org": data.get("org"),
                        "as": data.get("as")
                    }
        except Exception as e:
            self.add_warning(f"Geolocation lookup failed: {e}")
        
        return {}
    
    def _get_reverse_dns(self, ip: str) -> List[str]:
        """الحصول على Reverse DNS"""
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            return [hostname]
        except:
            return []
    
    def _advanced_dns_analysis(self, domain: str) -> Dict[str, Any]:
        """تحليل DNS متقدم"""
        # هذا مثال مبسط - يمكن توسيعه
        return {
            "mx_records": [],
            "txt_records": [],
            "ns_records": [],
            "cname_records": [],
            "soa_record": {}
        }
    
    def _find_subdomains(self, domain: str) -> List[str]:
        """البحث عن النطاقات الفرعية"""
        # هذا مثال مبسط - يمكن تطويره باستخدام APIs متخصصة
        common_subdomains = ["www", "mail", "ftp", "admin", "test", "dev", "api"]
        found_subdomains = []
        
        for subdomain in common_subdomains:
            try:
                full_domain = f"{subdomain}.{domain}"
                socket.gethostbyname(full_domain)
                found_subdomains.append(full_domain)
            except:
                pass
        
        return found_subdomains
    
    def _analyze_ssl_certificates(self, domain: str) -> Dict[str, Any]:
        """تحليل شهادات SSL"""
        # هذا مثال مبسط - يمكن توسيعه
        return {
            "has_ssl": False,
            "issuer": "",
            "subject": "",
            "valid_from": "",
            "valid_to": "",
            "signature_algorithm": ""
        }
    
    def _get_port_scan_history(self, ip: str) -> Dict[str, Any]:
        """الحصول على تاريخ فحص المنافذ"""
        # هذا مثال مبسط
        return {
            "last_scan": None,
            "open_ports_history": [],
            "scan_frequency": 0
        }
    
    def _identify_hash_type(self, hash_value: str) -> str:
        """تحديد نوع الهاش"""
        length = len(hash_value)
        if length == 32:
            return "MD5"
        elif length == 40:
            return "SHA1"
        elif length == 64:
            return "SHA256"
        elif length == 128:
            return "SHA512"
        else:
            return "Unknown"
    
    def _analyze_osint_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل نتائج OSINT"""
        analysis = {
            "risk_level": "low",
            "findings": [],
            "recommendations": [],
            "threat_indicators": []
        }
        
        # تحليل بيانات Shodan
        if "shodan" in data:
            shodan_data = data["shodan"]
            if shodan_data.get("vulnerabilities"):
                analysis["risk_level"] = "high"
                analysis["findings"].append("Known vulnerabilities detected")
                analysis["threat_indicators"].extend(shodan_data["vulnerabilities"])
            
            open_ports = shodan_data.get("ports", [])
            if len(open_ports) > 10:
                analysis["risk_level"] = "medium"
                analysis["findings"].append("Multiple open ports detected")
        
        # تحليل بيانات VirusTotal
        if "virustotal" in data:
            vt_data = data["virustotal"]
            if vt_data.get("positives", 0) > 0:
                analysis["risk_level"] = "high"
                analysis["findings"].append("Malware detected")
        
        # تحليل بيانات التسريبات
        if "breaches" in data:
            breach_data = data["breaches"]
            if breach_data.get("breach_count", 0) > 0:
                analysis["risk_level"] = "medium"
                analysis["findings"].append("Email found in data breaches")
        
        # توصيات
        if analysis["risk_level"] == "high":
            analysis["recommendations"].append("Immediate security review required")
        elif analysis["risk_level"] == "medium":
            analysis["recommendations"].append("Enhanced monitoring recommended")
        
        return analysis
    
    def _is_ip_address(self, target: str) -> bool:
        """فحص إذا كان IP address"""
        try:
            socket.inet_aton(target)
            return True
        except:
            return False
    
    def _is_domain(self, target: str) -> bool:
        """فحص إذا كان domain"""
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )
        return bool(domain_pattern.match(target))
    
    def _is_email(self, target: str) -> bool:
        """فحص إذا كان email"""
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        return bool(email_pattern.match(target))
    
    def _is_hash(self, target: str) -> bool:
        """فحص إذا كان hash"""
        return bool(re.match(r'^[a-fA-F0-9]+$', target) and len(target) in [32, 40, 64, 128])

# مثال على الاستخدام
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # إعدادات تجريبية (بدون مفاتيح API حقيقية)
    config = {
        "shodan_api_key": "",
        "virustotal_api_key": "",
        "haveibeenpwned_api_key": "",
        "api_delay": 1
    }
    
    # إنشاء الوحدة
    osint_module = AdvancedOSINTModule(config)
    osint_module.set_authorization(True, True)
    
    # تنفيذ OSINT على IP
    results = osint_module.start_execution("*******")
    print(json.dumps(results, indent=2, ensure_ascii=False))
