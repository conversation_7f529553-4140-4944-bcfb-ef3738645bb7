#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الاستطلاع الأساسية
Basic Reconnaissance Module
"""

import socket
import subprocess
import requests
import dns.resolver
import whois
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import ipaddress
import re
import json

# إضافة مسار المشروع للاستيراد
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.base_module import BaseSecurityModule, ModuleType

class BasicReconModule(BaseSecurityModule):
    """وحدة الاستطلاع الأساسية"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة وحدة الاستطلاع الأساسية
        
        Args:
            config: إعدادات الوحدة
        """
        super().__init__("basic_recon", ModuleType.RECONNAISSANCE, config)
        
        # إعدادات الوحدة
        self.timeout = self.config.get('timeout', 30)
        self.max_threads = self.config.get('max_threads', 5)
        self.user_agent = self.config.get('user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        # قوائم المنافذ الشائعة
        self.common_ports = [21, 22, 23, 25, 53, 80, 110, 111, 135, 139, 143, 
                           443, 993, 995, 1723, 3306, 3389, 5432, 5900, 8080]
        
        self.logger.info("Basic Reconnaissance Module initialized")
    
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: الهدف (IP أو domain)
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        try:
            # فحص إذا كان IP address
            ipaddress.ip_address(target)
            return True
        except ValueError:
            # فحص إذا كان domain name
            domain_pattern = re.compile(
                r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
            )
            return bool(domain_pattern.match(target))
    
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ الاستطلاع الأساسي
        
        Args:
            target: الهدف
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج الاستطلاع
        """
        try:
            self.log_activity("Starting basic reconnaissance", data={"target": target})
            
            results = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": {}
            }
            
            # تحديث التقدم
            self.update_progress(10, "Starting reconnaissance...")
            
            # معلومات DNS
            self.update_progress(20, "Gathering DNS information...")
            dns_info = self._gather_dns_info(target)
            results["data"]["dns"] = dns_info
            
            # معلومات WHOIS
            self.update_progress(40, "Gathering WHOIS information...")
            whois_info = self._gather_whois_info(target)
            results["data"]["whois"] = whois_info
            
            # فحص المنافذ
            self.update_progress(60, "Scanning common ports...")
            port_scan = self._scan_common_ports(target)
            results["data"]["ports"] = port_scan
            
            # معلومات HTTP (إذا كان المنفذ 80 أو 443 مفتوح)
            if any(port["port"] in [80, 443] and port["status"] == "open" 
                   for port in port_scan):
                self.update_progress(80, "Gathering HTTP information...")
                http_info = self._gather_http_info(target)
                results["data"]["http"] = http_info
            
            # تحليل النتائج
            self.update_progress(90, "Analyzing results...")
            analysis = self._analyze_results(results["data"])
            results["data"]["analysis"] = analysis
            
            self.update_progress(100, "Reconnaissance completed")
            
            self.log_activity("Basic reconnaissance completed", 
                            data={"target": target, "findings": len(results["data"])})
            
            return results
            
        except Exception as e:
            self.add_error(f"Reconnaissance failed: {str(e)}")
            return {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def _gather_dns_info(self, target: str) -> Dict[str, Any]:
        """
        جمع معلومات DNS
        
        Args:
            target: الهدف
            
        Returns:
            Dict: معلومات DNS
        """
        dns_info = {
            "records": {},
            "nameservers": [],
            "mx_records": []
        }
        
        try:
            # A records
            try:
                a_records = dns.resolver.resolve(target, 'A')
                dns_info["records"]["A"] = [str(record) for record in a_records]
            except:
                pass
            
            # AAAA records (IPv6)
            try:
                aaaa_records = dns.resolver.resolve(target, 'AAAA')
                dns_info["records"]["AAAA"] = [str(record) for record in aaaa_records]
            except:
                pass
            
            # CNAME records
            try:
                cname_records = dns.resolver.resolve(target, 'CNAME')
                dns_info["records"]["CNAME"] = [str(record) for record in cname_records]
            except:
                pass
            
            # MX records
            try:
                mx_records = dns.resolver.resolve(target, 'MX')
                dns_info["mx_records"] = [
                    {"priority": record.preference, "exchange": str(record.exchange)}
                    for record in mx_records
                ]
            except:
                pass
            
            # NS records
            try:
                ns_records = dns.resolver.resolve(target, 'NS')
                dns_info["nameservers"] = [str(record) for record in ns_records]
            except:
                pass
            
            # TXT records
            try:
                txt_records = dns.resolver.resolve(target, 'TXT')
                dns_info["records"]["TXT"] = [str(record) for record in txt_records]
            except:
                pass
            
        except Exception as e:
            self.add_warning(f"DNS lookup failed: {str(e)}")
        
        return dns_info
    
    def _gather_whois_info(self, target: str) -> Dict[str, Any]:
        """
        جمع معلومات WHOIS
        
        Args:
            target: الهدف
            
        Returns:
            Dict: معلومات WHOIS
        """
        whois_info = {}
        
        try:
            # تجنب IP addresses للـ WHOIS
            if not self._is_ip_address(target):
                w = whois.whois(target)
                
                whois_info = {
                    "domain_name": w.domain_name,
                    "registrar": w.registrar,
                    "creation_date": str(w.creation_date) if w.creation_date else None,
                    "expiration_date": str(w.expiration_date) if w.expiration_date else None,
                    "updated_date": str(w.updated_date) if w.updated_date else None,
                    "name_servers": w.name_servers if w.name_servers else [],
                    "status": w.status if w.status else [],
                    "emails": w.emails if w.emails else [],
                    "org": w.org,
                    "country": w.country
                }
                
        except Exception as e:
            self.add_warning(f"WHOIS lookup failed: {str(e)}")
        
        return whois_info
    
    def _scan_common_ports(self, target: str) -> List[Dict[str, Any]]:
        """
        فحص المنافذ الشائعة
        
        Args:
            target: الهدف
            
        Returns:
            List: نتائج فحص المنافذ
        """
        port_results = []
        
        # الحصول على IP address
        try:
            if self._is_ip_address(target):
                ip_address = target
            else:
                ip_address = socket.gethostbyname(target)
        except:
            self.add_warning(f"Could not resolve {target} to IP address")
            return port_results
        
        for port in self.common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((ip_address, port))
                
                port_info = {
                    "port": port,
                    "status": "open" if result == 0 else "closed",
                    "service": self._get_service_name(port)
                }
                
                # محاولة الحصول على banner
                if result == 0:
                    try:
                        sock.send(b'HEAD / HTTP/1.0\r\n\r\n')
                        banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
                        if banner:
                            port_info["banner"] = banner[:200]  # أول 200 حرف
                    except:
                        pass
                
                port_results.append(port_info)
                sock.close()
                
            except Exception as e:
                port_results.append({
                    "port": port,
                    "status": "error",
                    "error": str(e),
                    "service": self._get_service_name(port)
                })
        
        return port_results
    
    def _gather_http_info(self, target: str) -> Dict[str, Any]:
        """
        جمع معلومات HTTP
        
        Args:
            target: الهدف
            
        Returns:
            Dict: معلومات HTTP
        """
        http_info = {}
        
        protocols = ['http', 'https']
        
        for protocol in protocols:
            try:
                url = f"{protocol}://{target}"
                
                headers = {
                    'User-Agent': self.user_agent
                }
                
                response = requests.get(url, headers=headers, timeout=10, 
                                      verify=False, allow_redirects=True)
                
                http_info[protocol] = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "title": self._extract_title(response.text),
                    "server": response.headers.get('Server', 'Unknown'),
                    "content_length": len(response.content),
                    "final_url": response.url,
                    "redirects": len(response.history)
                }
                
                # فحص تقنيات الويب
                technologies = self._detect_web_technologies(response)
                if technologies:
                    http_info[protocol]["technologies"] = technologies
                
            except requests.exceptions.RequestException as e:
                http_info[protocol] = {
                    "error": str(e),
                    "accessible": False
                }
            except Exception as e:
                self.add_warning(f"HTTP info gathering failed for {protocol}: {str(e)}")
        
        return http_info
    
    def _analyze_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تحليل النتائج
        
        Args:
            data: بيانات الاستطلاع
            
        Returns:
            Dict: تحليل النتائج
        """
        analysis = {
            "risk_level": "low",
            "findings": [],
            "recommendations": [],
            "open_ports_count": 0,
            "services_detected": []
        }
        
        # تحليل المنافذ المفتوحة
        if "ports" in data:
            open_ports = [p for p in data["ports"] if p["status"] == "open"]
            analysis["open_ports_count"] = len(open_ports)
            analysis["services_detected"] = [p["service"] for p in open_ports]
            
            # تقييم المخاطر بناءً على المنافذ المفتوحة
            high_risk_ports = [21, 23, 135, 139, 445, 1433, 3389]
            if any(p["port"] in high_risk_ports for p in open_ports):
                analysis["risk_level"] = "high"
                analysis["findings"].append("High-risk ports detected")
            elif len(open_ports) > 5:
                analysis["risk_level"] = "medium"
                analysis["findings"].append("Multiple open ports detected")
        
        # تحليل معلومات HTTP
        if "http" in data:
            for protocol, info in data["http"].items():
                if isinstance(info, dict) and "server" in info:
                    server = info["server"].lower()
                    if "apache" in server or "nginx" in server or "iis" in server:
                        analysis["findings"].append(f"Web server detected: {info['server']}")
        
        # توصيات أمنية
        if analysis["open_ports_count"] > 0:
            analysis["recommendations"].append("Review open ports and close unnecessary services")
        
        if "whois" in data and data["whois"]:
            analysis["recommendations"].append("Ensure domain registration information is up to date")
        
        return analysis
    
    def _is_ip_address(self, target: str) -> bool:
        """فحص إذا كان الهدف IP address"""
        try:
            ipaddress.ip_address(target)
            return True
        except ValueError:
            return False
    
    def _get_service_name(self, port: int) -> str:
        """الحصول على اسم الخدمة للمنفذ"""
        services = {
            21: "FTP", 22: "SSH", 23: "Telnet", 25: "SMTP", 53: "DNS",
            80: "HTTP", 110: "POP3", 111: "RPC", 135: "RPC", 139: "NetBIOS",
            143: "IMAP", 443: "HTTPS", 993: "IMAPS", 995: "POP3S",
            1723: "PPTP", 3306: "MySQL", 3389: "RDP", 5432: "PostgreSQL",
            5900: "VNC", 8080: "HTTP-Alt"
        }
        return services.get(port, f"Unknown-{port}")
    
    def _extract_title(self, html: str) -> str:
        """استخراج عنوان الصفحة من HTML"""
        try:
            title_match = re.search(r'<title[^>]*>(.*?)</title>', html, re.IGNORECASE | re.DOTALL)
            if title_match:
                return title_match.group(1).strip()[:100]  # أول 100 حرف
        except:
            pass
        return "No title found"
    
    def _detect_web_technologies(self, response) -> List[str]:
        """كشف تقنيات الويب"""
        technologies = []
        
        # فحص headers
        headers = response.headers
        
        if 'X-Powered-By' in headers:
            technologies.append(f"Powered by: {headers['X-Powered-By']}")
        
        if 'Server' in headers:
            server = headers['Server']
            if 'Apache' in server:
                technologies.append("Apache")
            elif 'nginx' in server:
                technologies.append("Nginx")
            elif 'IIS' in server:
                technologies.append("IIS")
        
        # فحص محتوى الصفحة
        content = response.text.lower()
        
        if 'wordpress' in content:
            technologies.append("WordPress")
        if 'drupal' in content:
            technologies.append("Drupal")
        if 'joomla' in content:
            technologies.append("Joomla")
        if 'jquery' in content:
            technologies.append("jQuery")
        
        return technologies

# مثال على الاستخدام
if __name__ == "__main__":
    # إعداد التسجيل
    logging.basicConfig(level=logging.INFO)
    
    # إنشاء الوحدة
    recon_module = BasicReconModule()
    
    # تعيين التصريح
    recon_module.set_authorization(True, True)
    
    # تنفيذ الاستطلاع
    target = "example.com"
    results = recon_module.start_execution(target)
    
    # عرض النتائج
    print(json.dumps(results, indent=2, ensure_ascii=False))
