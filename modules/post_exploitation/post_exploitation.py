#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة ما بعد الاستغلال
Post-Exploitation Module
"""

import os
import sys
import platform
import subprocess
import psutil
import socket
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import hashlib
import base64
import getpass

# إضافة مسار المشروع للاستيراد
sys.path.append(str(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from core.base_module import BaseSecurityModule, ModuleType

class PostExploitationModule(BaseSecurityModule):
    """وحدة ما بعد الاستغلال لجمع المعلومات ورفع الصلاحيات"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة وحدة ما بعد الاستغلال
        
        Args:
            config: إعدادات الوحدة
        """
        super().__init__("post_exploitation", ModuleType.POST_EXPLOITATION, config)
        
        # إعدادات الوحدة
        self.collect_system_info = self.config.get('collect_system_info', True)
        self.collect_network_info = self.config.get('collect_network_info', True)
        self.collect_user_info = self.config.get('collect_user_info', True)
        self.collect_process_info = self.config.get('collect_process_info', True)
        self.attempt_privilege_escalation = self.config.get('attempt_privilege_escalation', False)
        self.max_data_size = self.config.get('max_data_size', 100 * 1024 * 1024)  # 100MB
        
        # قوائم الملفات والمجلدات الحساسة
        self.sensitive_files = {
            'windows': [
                'C:\\Windows\\System32\\config\\SAM',
                'C:\\Windows\\System32\\config\\SYSTEM',
                'C:\\Windows\\System32\\config\\SECURITY',
                'C:\\Users\\<USER>\\Desktop\\*.txt',
                'C:\\Users\\<USER>\\Documents\\*.doc*',
                'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Login Data'
            ],
            'linux': [
                '/etc/passwd',
                '/etc/shadow',
                '/etc/sudoers',
                '/home/<USER>/.ssh/id_rsa',
                '/home/<USER>/.bash_history',
                '/var/log/auth.log',
                '/etc/crontab'
            ]
        }
        
        self.logger.info("Post-Exploitation Module initialized")
    
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: الهدف (عادة "local" للنظام المحلي)
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        return target in ["local", "localhost"] or self._is_valid_ip(target)
    
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ عمليات ما بعد الاستغلال
        
        Args:
            target: الهدف
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج ما بعد الاستغلال
        """
        try:
            self.log_activity("Starting post-exploitation", data={"target": target})
            
            results = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": {}
            }
            
            # جمع معلومات النظام
            if self.collect_system_info:
                self.update_progress(15, "Collecting system information...")
                system_info = self._collect_system_information()
                results["data"]["system_info"] = system_info
            
            # جمع معلومات المستخدمين
            if self.collect_user_info:
                self.update_progress(30, "Collecting user information...")
                user_info = self._collect_user_information()
                results["data"]["user_info"] = user_info
            
            # جمع معلومات العمليات
            if self.collect_process_info:
                self.update_progress(45, "Collecting process information...")
                process_info = self._collect_process_information()
                results["data"]["process_info"] = process_info
            
            # جمع معلومات الشبكة
            if self.collect_network_info:
                self.update_progress(60, "Collecting network information...")
                network_info = self._collect_network_information()
                results["data"]["network_info"] = network_info
            
            # البحث عن الملفات الحساسة
            self.update_progress(75, "Searching for sensitive files...")
            sensitive_files = self._search_sensitive_files()
            results["data"]["sensitive_files"] = sensitive_files
            
            # محاولة رفع الصلاحيات
            if self.attempt_privilege_escalation:
                self.update_progress(85, "Attempting privilege escalation...")
                privesc_results = self._attempt_privilege_escalation()
                results["data"]["privilege_escalation"] = privesc_results
            
            # تحليل النتائج
            self.update_progress(95, "Analyzing post-exploitation results...")
            analysis = self._analyze_post_exploitation_results(results["data"])
            results["data"]["analysis"] = analysis
            
            self.update_progress(100, "Post-exploitation completed")
            
            self.log_activity("Post-exploitation completed", 
                            data={"target": target, "data_collected": len(results["data"])})
            
            return results
            
        except Exception as e:
            self.add_error(f"Post-exploitation failed: {str(e)}")
            return {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def _collect_system_information(self) -> Dict[str, Any]:
        """جمع معلومات النظام"""
        system_info = {}
        
        try:
            # معلومات النظام الأساسية
            system_info["platform"] = platform.platform()
            system_info["system"] = platform.system()
            system_info["release"] = platform.release()
            system_info["version"] = platform.version()
            system_info["machine"] = platform.machine()
            system_info["processor"] = platform.processor()
            system_info["hostname"] = socket.gethostname()
            
            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            system_info["memory"] = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free
            }
            
            # معلومات القرص الصلب
            disk_usage = psutil.disk_usage('/')
            system_info["disk"] = {
                "total": disk_usage.total,
                "used": disk_usage.used,
                "free": disk_usage.free,
                "percent": (disk_usage.used / disk_usage.total) * 100
            }
            
            # معلومات المعالج
            system_info["cpu"] = {
                "physical_cores": psutil.cpu_count(logical=False),
                "total_cores": psutil.cpu_count(logical=True),
                "max_frequency": psutil.cpu_freq().max if psutil.cpu_freq() else None,
                "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
                "cpu_usage": psutil.cpu_percent(interval=1)
            }
            
            # متغيرات البيئة
            system_info["environment_variables"] = dict(os.environ)
            
            # وقت التشغيل
            boot_time = psutil.boot_time()
            system_info["boot_time"] = datetime.fromtimestamp(boot_time).isoformat()
            system_info["uptime_seconds"] = datetime.now().timestamp() - boot_time
            
        except Exception as e:
            self.add_warning(f"System information collection failed: {e}")
        
        return system_info
    
    def _collect_user_information(self) -> Dict[str, Any]:
        """جمع معلومات المستخدمين"""
        user_info = {}
        
        try:
            # المستخدم الحالي
            user_info["current_user"] = getpass.getuser()
            user_info["user_id"] = os.getuid() if hasattr(os, 'getuid') else None
            user_info["group_id"] = os.getgid() if hasattr(os, 'getgid') else None
            
            # المستخدمين المتصلين
            users = psutil.users()
            user_info["logged_in_users"] = [
                {
                    "name": user.name,
                    "terminal": user.terminal,
                    "host": user.host,
                    "started": datetime.fromtimestamp(user.started).isoformat()
                }
                for user in users
            ]
            
            # معلومات إضافية حسب النظام
            if platform.system() == "Windows":
                user_info.update(self._collect_windows_user_info())
            else:
                user_info.update(self._collect_unix_user_info())
            
        except Exception as e:
            self.add_warning(f"User information collection failed: {e}")
        
        return user_info
    
    def _collect_windows_user_info(self) -> Dict[str, Any]:
        """جمع معلومات المستخدمين في Windows"""
        windows_info = {}
        
        try:
            # قائمة المستخدمين المحليين
            result = subprocess.run(['net', 'user'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                windows_info["local_users"] = result.stdout
            
            # معلومات المجموعات
            result = subprocess.run(['net', 'localgroup'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                windows_info["local_groups"] = result.stdout
            
            # معلومات الصلاحيات
            result = subprocess.run(['whoami', '/priv'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                windows_info["privileges"] = result.stdout
            
        except Exception as e:
            self.add_warning(f"Windows user info collection failed: {e}")
        
        return windows_info
    
    def _collect_unix_user_info(self) -> Dict[str, Any]:
        """جمع معلومات المستخدمين في Unix/Linux"""
        unix_info = {}
        
        try:
            # قراءة /etc/passwd
            if os.path.exists('/etc/passwd'):
                with open('/etc/passwd', 'r') as f:
                    passwd_content = f.read()
                    unix_info["passwd_file"] = passwd_content[:5000]  # أول 5000 حرف
            
            # معلومات sudo
            result = subprocess.run(['sudo', '-l'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                unix_info["sudo_privileges"] = result.stdout
            
            # معلومات المجموعات
            result = subprocess.run(['groups'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                unix_info["user_groups"] = result.stdout.strip()
            
        except Exception as e:
            self.add_warning(f"Unix user info collection failed: {e}")
        
        return unix_info
    
    def _collect_process_information(self) -> Dict[str, Any]:
        """جمع معلومات العمليات"""
        process_info = {
            "running_processes": [],
            "process_count": 0,
            "high_cpu_processes": [],
            "high_memory_processes": []
        }
        
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_info['cpu_percent'] = proc.cpu_percent()
                    proc_info['memory_percent'] = proc.memory_percent()
                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            process_info["process_count"] = len(processes)
            process_info["running_processes"] = processes[:50]  # أول 50 عملية
            
            # العمليات عالية استهلاك المعالج
            high_cpu = sorted(processes, key=lambda x: x.get('cpu_percent', 0), reverse=True)[:10]
            process_info["high_cpu_processes"] = high_cpu
            
            # العمليات عالية استهلاك الذاكرة
            high_memory = sorted(processes, key=lambda x: x.get('memory_percent', 0), reverse=True)[:10]
            process_info["high_memory_processes"] = high_memory
            
        except Exception as e:
            self.add_warning(f"Process information collection failed: {e}")
        
        return process_info
    
    def _collect_network_information(self) -> Dict[str, Any]:
        """جمع معلومات الشبكة"""
        network_info = {}
        
        try:
            # معلومات الشبكة الأساسية
            network_info["hostname"] = socket.gethostname()
            network_info["fqdn"] = socket.getfqdn()
            
            # عناوين IP
            hostname = socket.gethostname()
            network_info["ip_addresses"] = socket.gethostbyname_ex(hostname)[2]
            
            # واجهات الشبكة
            interfaces = psutil.net_if_addrs()
            network_info["network_interfaces"] = {}
            for interface, addresses in interfaces.items():
                network_info["network_interfaces"][interface] = [
                    {
                        "family": addr.family.name,
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    }
                    for addr in addresses
                ]
            
            # الاتصالات النشطة
            connections = psutil.net_connections()
            network_info["active_connections"] = [
                {
                    "family": conn.family.name,
                    "type": conn.type.name,
                    "local_address": f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                    "remote_address": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                    "status": conn.status,
                    "pid": conn.pid
                }
                for conn in connections[:50]  # أول 50 اتصال
            ]
            
            # إحصائيات الشبكة
            net_io = psutil.net_io_counters()
            network_info["network_stats"] = {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv,
                "errin": net_io.errin,
                "errout": net_io.errout,
                "dropin": net_io.dropin,
                "dropout": net_io.dropout
            }
            
        except Exception as e:
            self.add_warning(f"Network information collection failed: {e}")
        
        return network_info
    
    def _search_sensitive_files(self) -> Dict[str, Any]:
        """البحث عن الملفات الحساسة"""
        sensitive_files = {
            "found_files": [],
            "accessible_files": [],
            "search_completed": False
        }
        
        try:
            system_type = platform.system().lower()
            if system_type == "windows":
                file_list = self.sensitive_files.get('windows', [])
            else:
                file_list = self.sensitive_files.get('linux', [])
            
            for file_pattern in file_list:
                try:
                    # البحث عن الملفات (مبسط)
                    if '*' not in file_pattern:
                        if os.path.exists(file_pattern):
                            sensitive_files["found_files"].append(file_pattern)
                            
                            # فحص إمكانية القراءة
                            if os.access(file_pattern, os.R_OK):
                                file_info = {
                                    "path": file_pattern,
                                    "size": os.path.getsize(file_pattern),
                                    "modified": datetime.fromtimestamp(os.path.getmtime(file_pattern)).isoformat(),
                                    "permissions": oct(os.stat(file_pattern).st_mode)[-3:]
                                }
                                sensitive_files["accessible_files"].append(file_info)
                except Exception:
                    continue
            
            sensitive_files["search_completed"] = True
            
        except Exception as e:
            self.add_warning(f"Sensitive files search failed: {e}")
        
        return sensitive_files
    
    def _attempt_privilege_escalation(self) -> Dict[str, Any]:
        """محاولة رفع الصلاحيات"""
        privesc_results = {
            "attempted": True,
            "successful": False,
            "techniques_tried": [],
            "current_privileges": None,
            "escalation_paths": []
        }
        
        try:
            # فحص الصلاحيات الحالية
            current_user = getpass.getuser()
            privesc_results["current_privileges"] = current_user
            
            # تقنيات رفع الصلاحيات حسب النظام
            if platform.system() == "Windows":
                privesc_results.update(self._windows_privilege_escalation())
            else:
                privesc_results.update(self._unix_privilege_escalation())
            
        except Exception as e:
            self.add_warning(f"Privilege escalation failed: {e}")
        
        return privesc_results
    
    def _windows_privilege_escalation(self) -> Dict[str, Any]:
        """تقنيات رفع الصلاحيات في Windows"""
        windows_privesc = {
            "techniques_tried": [],
            "escalation_paths": []
        }
        
        try:
            # فحص الخدمات غير المحمية
            windows_privesc["techniques_tried"].append("Unquoted service paths check")
            
            # فحص مهام الجدولة
            windows_privesc["techniques_tried"].append("Scheduled tasks check")
            
            # فحص التطبيقات المثبتة
            windows_privesc["techniques_tried"].append("Installed applications check")
            
        except Exception as e:
            self.add_warning(f"Windows privilege escalation failed: {e}")
        
        return windows_privesc
    
    def _unix_privilege_escalation(self) -> Dict[str, Any]:
        """تقنيات رفع الصلاحيات في Unix/Linux"""
        unix_privesc = {
            "techniques_tried": [],
            "escalation_paths": []
        }
        
        try:
            # فحص sudo
            unix_privesc["techniques_tried"].append("Sudo configuration check")
            
            # فحص SUID binaries
            unix_privesc["techniques_tried"].append("SUID binaries check")
            
            # فحص cron jobs
            unix_privesc["techniques_tried"].append("Cron jobs check")
            
        except Exception as e:
            self.add_warning(f"Unix privilege escalation failed: {e}")
        
        return unix_privesc
    
    def _analyze_post_exploitation_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل نتائج ما بعد الاستغلال"""
        analysis = {
            "data_collected": True,
            "sensitive_data_found": False,
            "privilege_escalation_possible": False,
            "risk_assessment": "medium",
            "recommendations": [],
            "next_steps": []
        }
        
        try:
            # تحليل الملفات الحساسة
            sensitive_files = data.get("sensitive_files", {})
            if sensitive_files.get("accessible_files"):
                analysis["sensitive_data_found"] = True
                analysis["risk_assessment"] = "high"
                analysis["recommendations"].append("Secure sensitive files with proper permissions")
            
            # تحليل رفع الصلاحيات
            privesc = data.get("privilege_escalation", {})
            if privesc.get("escalation_paths"):
                analysis["privilege_escalation_possible"] = True
                analysis["risk_assessment"] = "critical"
                analysis["recommendations"].append("Address privilege escalation vulnerabilities")
            
            # تحليل معلومات المستخدمين
            user_info = data.get("user_info", {})
            if user_info.get("current_user") == "root" or user_info.get("current_user") == "Administrator":
                analysis["risk_assessment"] = "critical"
                analysis["recommendations"].append("System compromised with administrative privileges")
            
            # خطوات تالية
            analysis["next_steps"] = [
                "Establish persistent access",
                "Lateral movement reconnaissance",
                "Data exfiltration preparation",
                "Cover tracks and maintain stealth"
            ]
            
        except Exception as e:
            self.add_warning(f"Post-exploitation analysis failed: {e}")
        
        return analysis
    
    def _is_valid_ip(self, ip: str) -> bool:
        """فحص صحة عنوان IP"""
        try:
            socket.inet_aton(ip)
            return True
        except:
            return False

# مثال على الاستخدام
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # إنشاء الوحدة
    post_exploit = PostExploitationModule()
    post_exploit.set_authorization(True, True)
    
    # تنفيذ ما بعد الاستغلال على النظام المحلي
    results = post_exploit.start_execution("local")
    print(json.dumps(results, indent=2, ensure_ascii=False))
