#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرك الذكاء الاصطناعي
AI Engine for Security Framework
"""

import numpy as np
import pandas as pd
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pickle
import os
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
import joblib

# إضافة مسار المشروع للاستيراد
import sys
sys.path.append(str(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from core.base_module import BaseSecurityModule, ModuleType

class AISecurityEngine(BaseSecurityModule):
    """محرك الذكاء الاصطناعي للأمان السيبراني"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة محرك الذكاء الاصطناعي
        
        Args:
            config: إعدادات المحرك
        """
        super().__init__("ai_security_engine", ModuleType.INTELLIGENCE, config)
        
        # إعدادات النماذج
        self.models_dir = self.config.get('models_dir', 'data/models')
        self.enable_vulnerability_prediction = self.config.get('enable_vulnerability_prediction', True)
        self.enable_exploit_success_prediction = self.config.get('enable_exploit_success_prediction', True)
        self.enable_threat_classification = self.config.get('enable_threat_classification', True)
        self.enable_anomaly_detection = self.config.get('enable_anomaly_detection', True)
        
        # إنشاء مجلد النماذج
        os.makedirs(self.models_dir, exist_ok=True)
        
        # النماذج المدربة
        self.vulnerability_model = None
        self.exploit_success_model = None
        self.threat_classifier = None
        self.anomaly_detector = None
        
        # معالجات البيانات
        self.scalers = {}
        self.encoders = {}
        
        # تحميل النماذج المدربة
        self._load_models()
        
        self.logger.info("AI Security Engine initialized")
    
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: نوع التحليل المطلوب
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        valid_targets = [
            "vulnerability_assessment",
            "exploit_prediction", 
            "threat_analysis",
            "anomaly_detection",
            "comprehensive_analysis"
        ]
        return target in valid_targets
    
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ التحليل بالذكاء الاصطناعي
        
        Args:
            target: نوع التحليل
            **kwargs: بيانات التحليل
            
        Returns:
            Dict: نتائج التحليل
        """
        try:
            self.log_activity("Starting AI analysis", data={"target": target})
            
            results = {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": {}
            }
            
            # تنفيذ التحليل حسب النوع
            if target == "vulnerability_assessment":
                self.update_progress(25, "Performing vulnerability assessment...")
                vuln_results = self._assess_vulnerabilities(kwargs.get('vulnerability_data', {}))
                results["data"]["vulnerability_assessment"] = vuln_results
                
            elif target == "exploit_prediction":
                self.update_progress(25, "Predicting exploit success...")
                exploit_results = self._predict_exploit_success(kwargs.get('exploit_data', {}))
                results["data"]["exploit_prediction"] = exploit_results
                
            elif target == "threat_analysis":
                self.update_progress(25, "Analyzing threats...")
                threat_results = self._analyze_threats(kwargs.get('threat_data', {}))
                results["data"]["threat_analysis"] = threat_results
                
            elif target == "anomaly_detection":
                self.update_progress(25, "Detecting anomalies...")
                anomaly_results = self._detect_anomalies(kwargs.get('network_data', {}))
                results["data"]["anomaly_detection"] = anomaly_results
                
            elif target == "comprehensive_analysis":
                self.update_progress(10, "Starting comprehensive analysis...")
                
                # تحليل شامل لجميع الجوانب
                if kwargs.get('vulnerability_data'):
                    self.update_progress(25, "Vulnerability assessment...")
                    results["data"]["vulnerability_assessment"] = self._assess_vulnerabilities(kwargs['vulnerability_data'])
                
                if kwargs.get('exploit_data'):
                    self.update_progress(45, "Exploit prediction...")
                    results["data"]["exploit_prediction"] = self._predict_exploit_success(kwargs['exploit_data'])
                
                if kwargs.get('threat_data'):
                    self.update_progress(65, "Threat analysis...")
                    results["data"]["threat_analysis"] = self._analyze_threats(kwargs['threat_data'])
                
                if kwargs.get('network_data'):
                    self.update_progress(85, "Anomaly detection...")
                    results["data"]["anomaly_detection"] = self._detect_anomalies(kwargs['network_data'])
            
            # تحليل النتائج وتوليد التوصيات
            self.update_progress(95, "Generating recommendations...")
            recommendations = self._generate_ai_recommendations(results["data"])
            results["data"]["recommendations"] = recommendations
            
            self.update_progress(100, "AI analysis completed")
            
            self.log_activity("AI analysis completed", 
                            data={"target": target, "analyses": len(results["data"])})
            
            return results
            
        except Exception as e:
            self.add_error(f"AI analysis failed: {str(e)}")
            return {
                "target": target,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    
    def _assess_vulnerabilities(self, vuln_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم الثغرات باستخدام الذكاء الاصطناعي"""
        assessment = {
            "total_vulnerabilities": 0,
            "critical_vulnerabilities": [],
            "risk_score": 0.0,
            "exploitability_scores": {},
            "remediation_priority": []
        }
        
        try:
            vulnerabilities = vuln_data.get('vulnerabilities', [])
            assessment["total_vulnerabilities"] = len(vulnerabilities)
            
            for vuln in vulnerabilities:
                # تحليل الثغرة
                vuln_analysis = self._analyze_single_vulnerability(vuln)
                
                # تصنيف الخطورة
                if vuln_analysis['risk_level'] == 'critical':
                    assessment["critical_vulnerabilities"].append({
                        "cve_id": vuln.get('cve_id', 'Unknown'),
                        "cvss_score": vuln.get('cvss_score', 0),
                        "exploitability": vuln_analysis['exploitability_score'],
                        "impact": vuln_analysis['impact_score']
                    })
                
                # نقاط القابلية للاستغلال
                assessment["exploitability_scores"][vuln.get('cve_id', 'Unknown')] = vuln_analysis['exploitability_score']
            
            # حساب نقاط المخاطر الإجمالية
            assessment["risk_score"] = self._calculate_overall_risk_score(vulnerabilities)
            
            # ترتيب أولويات المعالجة
            assessment["remediation_priority"] = self._prioritize_remediation(vulnerabilities)
            
        except Exception as e:
            self.add_warning(f"Vulnerability assessment failed: {e}")
        
        return assessment
    
    def _analyze_single_vulnerability(self, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل ثغرة واحدة"""
        analysis = {
            "risk_level": "low",
            "exploitability_score": 0.0,
            "impact_score": 0.0,
            "factors": []
        }
        
        try:
            cvss_score = vuln.get('cvss_score', 0)
            
            # تحديد مستوى المخاطر
            if cvss_score >= 9.0:
                analysis["risk_level"] = "critical"
            elif cvss_score >= 7.0:
                analysis["risk_level"] = "high"
            elif cvss_score >= 4.0:
                analysis["risk_level"] = "medium"
            else:
                analysis["risk_level"] = "low"
            
            # حساب نقاط القابلية للاستغلال
            exploitability_factors = []
            
            # وجود استغلال عام
            if vuln.get('exploit_available', False):
                exploitability_factors.append(0.3)
                analysis["factors"].append("Public exploit available")
            
            # عمر الثغرة
            published_date = vuln.get('published_date')
            if published_date:
                try:
                    pub_date = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                    age_days = (datetime.now() - pub_date.replace(tzinfo=None)).days
                    if age_days > 365:
                        exploitability_factors.append(0.2)
                        analysis["factors"].append("Old vulnerability (>1 year)")
                    elif age_days > 90:
                        exploitability_factors.append(0.1)
                        analysis["factors"].append("Mature vulnerability (>3 months)")
                except:
                    pass
            
            # نوع الثغرة
            description = vuln.get('description', '').lower()
            if any(keyword in description for keyword in ['remote', 'network', 'unauthenticated']):
                exploitability_factors.append(0.25)
                analysis["factors"].append("Remote/Network exploitable")
            
            if any(keyword in description for keyword in ['buffer overflow', 'code execution', 'rce']):
                exploitability_factors.append(0.3)
                analysis["factors"].append("Code execution possible")
            
            # حساب النقاط النهائية
            analysis["exploitability_score"] = min(sum(exploitability_factors), 1.0)
            analysis["impact_score"] = cvss_score / 10.0
            
        except Exception as e:
            self.add_warning(f"Single vulnerability analysis failed: {e}")
        
        return analysis
    
    def _predict_exploit_success(self, exploit_data: Dict[str, Any]) -> Dict[str, Any]:
        """التنبؤ بنجاح الاستغلال"""
        prediction = {
            "success_probability": 0.0,
            "confidence": 0.0,
            "factors": [],
            "recommendations": []
        }
        
        try:
            # عوامل نجاح الاستغلال
            success_factors = []
            
            # نوع الهدف
            target_type = exploit_data.get('target_type', 'unknown')
            if target_type == 'web_application':
                success_factors.append(0.7)
                prediction["factors"].append("Web application target (higher success rate)")
            elif target_type == 'network_service':
                success_factors.append(0.6)
                prediction["factors"].append("Network service target")
            elif target_type == 'local_system':
                success_factors.append(0.8)
                prediction["factors"].append("Local system access")
            
            # وجود دفاعات
            defenses = exploit_data.get('defenses', [])
            if 'firewall' in defenses:
                success_factors.append(-0.2)
                prediction["factors"].append("Firewall detected (reduces success)")
            if 'antivirus' in defenses:
                success_factors.append(-0.3)
                prediction["factors"].append("Antivirus detected (reduces success)")
            if 'ids_ips' in defenses:
                success_factors.append(-0.25)
                prediction["factors"].append("IDS/IPS detected (reduces success)")
            
            # خبرة المهاجم
            attacker_skill = exploit_data.get('attacker_skill', 'intermediate')
            if attacker_skill == 'expert':
                success_factors.append(0.3)
                prediction["factors"].append("Expert attacker skill level")
            elif attacker_skill == 'intermediate':
                success_factors.append(0.1)
                prediction["factors"].append("Intermediate attacker skill level")
            else:
                success_factors.append(-0.1)
                prediction["factors"].append("Beginner attacker skill level")
            
            # تعقيد الاستغلال
            complexity = exploit_data.get('exploit_complexity', 'medium')
            if complexity == 'low':
                success_factors.append(0.2)
                prediction["factors"].append("Low exploit complexity")
            elif complexity == 'high':
                success_factors.append(-0.2)
                prediction["factors"].append("High exploit complexity")
            
            # حساب الاحتمالية النهائية
            base_probability = 0.5
            total_factor = sum(success_factors)
            prediction["success_probability"] = max(0.0, min(1.0, base_probability + total_factor))
            
            # حساب الثقة
            prediction["confidence"] = min(0.9, 0.6 + len(prediction["factors"]) * 0.05)
            
            # توصيات
            if prediction["success_probability"] > 0.7:
                prediction["recommendations"].append("High success probability - proceed with caution")
                prediction["recommendations"].append("Ensure proper authorization and legal compliance")
            elif prediction["success_probability"] > 0.4:
                prediction["recommendations"].append("Moderate success probability - consider additional reconnaissance")
            else:
                prediction["recommendations"].append("Low success probability - improve attack vector or gather more intelligence")
            
        except Exception as e:
            self.add_warning(f"Exploit success prediction failed: {e}")
        
        return prediction
    
    def _analyze_threats(self, threat_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل التهديدات"""
        analysis = {
            "threat_level": "low",
            "threat_types": [],
            "attack_patterns": [],
            "indicators_of_compromise": [],
            "mitigation_strategies": []
        }
        
        try:
            threats = threat_data.get('threats', [])
            
            # تصنيف التهديدات
            threat_categories = {}
            for threat in threats:
                threat_type = threat.get('type', 'unknown')
                if threat_type not in threat_categories:
                    threat_categories[threat_type] = []
                threat_categories[threat_type].append(threat)
            
            analysis["threat_types"] = list(threat_categories.keys())
            
            # تحليل أنماط الهجوم
            attack_patterns = self._identify_attack_patterns(threats)
            analysis["attack_patterns"] = attack_patterns
            
            # مؤشرات الاختراق
            iocs = self._extract_iocs(threats)
            analysis["indicators_of_compromise"] = iocs
            
            # تحديد مستوى التهديد
            high_severity_count = sum(1 for threat in threats if threat.get('severity', 'low') in ['high', 'critical'])
            if high_severity_count > 5:
                analysis["threat_level"] = "critical"
            elif high_severity_count > 2:
                analysis["threat_level"] = "high"
            elif len(threats) > 10:
                analysis["threat_level"] = "medium"
            
            # استراتيجيات التخفيف
            analysis["mitigation_strategies"] = self._generate_mitigation_strategies(threat_categories)
            
        except Exception as e:
            self.add_warning(f"Threat analysis failed: {e}")
        
        return analysis
    
    def _detect_anomalies(self, network_data: Dict[str, Any]) -> Dict[str, Any]:
        """كشف الشذوذ في البيانات"""
        detection = {
            "anomalies_detected": False,
            "anomaly_count": 0,
            "anomaly_types": [],
            "suspicious_activities": [],
            "confidence_scores": {}
        }
        
        try:
            # تحليل حركة المرور
            traffic_data = network_data.get('traffic', [])
            
            if traffic_data:
                # كشف الشذوذ في حجم البيانات
                traffic_volumes = [entry.get('total_bytes', 0) for entry in traffic_data]
                if traffic_volumes:
                    mean_volume = np.mean(traffic_volumes)
                    std_volume = np.std(traffic_volumes)
                    
                    for i, volume in enumerate(traffic_volumes):
                        if abs(volume - mean_volume) > 2 * std_volume:
                            detection["anomalies_detected"] = True
                            detection["anomaly_count"] += 1
                            detection["suspicious_activities"].append({
                                "type": "unusual_traffic_volume",
                                "timestamp": traffic_data[i].get('timestamp'),
                                "volume": volume,
                                "deviation": abs(volume - mean_volume) / std_volume
                            })
                
                # كشف الشذوذ في أنماط الاتصال
                connection_patterns = self._analyze_connection_patterns(traffic_data)
                if connection_patterns.get('anomalies'):
                    detection["anomalies_detected"] = True
                    detection["anomaly_types"].extend(connection_patterns['anomalies'])
            
            # تحليل سجلات الأمان
            security_logs = network_data.get('security_logs', [])
            if security_logs:
                log_anomalies = self._analyze_security_logs(security_logs)
                if log_anomalies:
                    detection["anomalies_detected"] = True
                    detection["suspicious_activities"].extend(log_anomalies)
            
        except Exception as e:
            self.add_warning(f"Anomaly detection failed: {e}")
        
        return detection
    
    def _generate_ai_recommendations(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """توليد التوصيات بناءً على التحليل"""
        recommendations = {
            "immediate_actions": [],
            "short_term_actions": [],
            "long_term_actions": [],
            "priority_level": "medium"
        }
        
        try:
            # تحليل تقييم الثغرات
            if "vulnerability_assessment" in analysis_data:
                vuln_data = analysis_data["vulnerability_assessment"]
                if vuln_data.get("critical_vulnerabilities"):
                    recommendations["immediate_actions"].append("Patch critical vulnerabilities immediately")
                    recommendations["priority_level"] = "high"
                
                if vuln_data.get("risk_score", 0) > 7.0:
                    recommendations["immediate_actions"].append("Implement emergency security measures")
                    recommendations["priority_level"] = "critical"
            
            # تحليل التنبؤ بالاستغلال
            if "exploit_prediction" in analysis_data:
                exploit_data = analysis_data["exploit_prediction"]
                if exploit_data.get("success_probability", 0) > 0.7:
                    recommendations["immediate_actions"].append("High exploit risk detected - enhance monitoring")
                    recommendations["short_term_actions"].append("Review and strengthen security controls")
            
            # تحليل التهديدات
            if "threat_analysis" in analysis_data:
                threat_data = analysis_data["threat_analysis"]
                if threat_data.get("threat_level") in ["high", "critical"]:
                    recommendations["immediate_actions"].append("Activate incident response procedures")
                    recommendations["short_term_actions"].append("Implement threat hunting activities")
            
            # كشف الشذوذ
            if "anomaly_detection" in analysis_data:
                anomaly_data = analysis_data["anomaly_detection"]
                if anomaly_data.get("anomalies_detected"):
                    recommendations["immediate_actions"].append("Investigate detected anomalies")
                    recommendations["short_term_actions"].append("Enhance network monitoring")
            
            # توصيات طويلة المدى
            recommendations["long_term_actions"] = [
                "Implement continuous security monitoring",
                "Regular security assessments and penetration testing",
                "Security awareness training for staff",
                "Develop and test incident response procedures",
                "Implement zero-trust security architecture"
            ]
            
        except Exception as e:
            self.add_warning(f"AI recommendations generation failed: {e}")
        
        return recommendations
    
    def _calculate_overall_risk_score(self, vulnerabilities: List[Dict]) -> float:
        """حساب نقاط المخاطر الإجمالية"""
        if not vulnerabilities:
            return 0.0
        
        total_score = 0.0
        for vuln in vulnerabilities:
            cvss_score = vuln.get('cvss_score', 0)
            exploit_available = vuln.get('exploit_available', False)
            
            # وزن إضافي للثغرات التي لها استغلالات متاحة
            weight = 1.5 if exploit_available else 1.0
            total_score += cvss_score * weight
        
        # متوسط مرجح
        return min(10.0, total_score / len(vulnerabilities))
    
    def _prioritize_remediation(self, vulnerabilities: List[Dict]) -> List[Dict]:
        """ترتيب أولويات معالجة الثغرات"""
        prioritized = []
        
        for vuln in vulnerabilities:
            priority_score = vuln.get('cvss_score', 0)
            
            # عوامل إضافية للأولوية
            if vuln.get('exploit_available', False):
                priority_score += 2.0
            
            # عمر الثغرة
            published_date = vuln.get('published_date')
            if published_date:
                try:
                    pub_date = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                    age_days = (datetime.now() - pub_date.replace(tzinfo=None)).days
                    if age_days > 365:
                        priority_score += 1.0
                except:
                    pass
            
            prioritized.append({
                "cve_id": vuln.get('cve_id', 'Unknown'),
                "priority_score": priority_score,
                "cvss_score": vuln.get('cvss_score', 0),
                "exploit_available": vuln.get('exploit_available', False)
            })
        
        # ترتيب حسب نقاط الأولوية
        return sorted(prioritized, key=lambda x: x['priority_score'], reverse=True)
    
    def _identify_attack_patterns(self, threats: List[Dict]) -> List[str]:
        """تحديد أنماط الهجوم"""
        patterns = []
        
        # تحليل مبسط لأنماط الهجوم
        threat_types = [threat.get('type', '') for threat in threats]
        
        if 'phishing' in threat_types and 'malware' in threat_types:
            patterns.append("Multi-stage attack pattern detected")
        
        if threat_types.count('brute_force') > 3:
            patterns.append("Coordinated brute force attack pattern")
        
        if 'reconnaissance' in threat_types:
            patterns.append("Pre-attack reconnaissance detected")
        
        return patterns
    
    def _extract_iocs(self, threats: List[Dict]) -> List[Dict]:
        """استخراج مؤشرات الاختراق"""
        iocs = []
        
        for threat in threats:
            if threat.get('indicator_value'):
                iocs.append({
                    "type": threat.get('indicator_type', 'unknown'),
                    "value": threat.get('indicator_value'),
                    "confidence": threat.get('confidence_score', 0.5),
                    "source": threat.get('source', 'unknown')
                })
        
        return iocs
    
    def _generate_mitigation_strategies(self, threat_categories: Dict) -> List[str]:
        """توليد استراتيجيات التخفيف"""
        strategies = []
        
        for threat_type, threats in threat_categories.items():
            if threat_type == 'malware':
                strategies.append("Deploy advanced anti-malware solutions")
                strategies.append("Implement application whitelisting")
            elif threat_type == 'phishing':
                strategies.append("Enhance email security and filtering")
                strategies.append("Conduct phishing awareness training")
            elif threat_type == 'brute_force':
                strategies.append("Implement account lockout policies")
                strategies.append("Deploy multi-factor authentication")
            elif threat_type == 'reconnaissance':
                strategies.append("Limit information disclosure")
                strategies.append("Monitor for scanning activities")
        
        return list(set(strategies))  # إزالة التكرار
    
    def _analyze_connection_patterns(self, traffic_data: List[Dict]) -> Dict:
        """تحليل أنماط الاتصال"""
        analysis = {"anomalies": []}
        
        # تحليل مبسط لأنماط الاتصال
        connection_counts = {}
        for entry in traffic_data:
            timestamp = entry.get('timestamp', '')
            hour = timestamp[:13] if len(timestamp) >= 13 else timestamp  # استخراج الساعة
            
            if hour not in connection_counts:
                connection_counts[hour] = 0
            connection_counts[hour] += entry.get('unique_ips', 0)
        
        # كشف الشذوذ في عدد الاتصالات
        if connection_counts:
            values = list(connection_counts.values())
            mean_connections = np.mean(values)
            std_connections = np.std(values)
            
            for hour, count in connection_counts.items():
                if count > mean_connections + 2 * std_connections:
                    analysis["anomalies"].append("Unusual connection spike detected")
                    break
        
        return analysis
    
    def _analyze_security_logs(self, security_logs: List[Dict]) -> List[Dict]:
        """تحليل سجلات الأمان"""
        anomalies = []
        
        # تحليل مبسط لسجلات الأمان
        failed_logins = [log for log in security_logs if log.get('event_type') == 'failed_login']
        
        if len(failed_logins) > 10:
            anomalies.append({
                "type": "excessive_failed_logins",
                "count": len(failed_logins),
                "description": "Unusual number of failed login attempts detected"
            })
        
        return anomalies
    
    def _load_models(self):
        """تحميل النماذج المدربة"""
        try:
            # محاولة تحميل النماذج المحفوظة
            model_files = {
                'vulnerability_model': 'vulnerability_classifier.joblib',
                'exploit_success_model': 'exploit_predictor.joblib',
                'threat_classifier': 'threat_classifier.joblib',
                'anomaly_detector': 'anomaly_detector.joblib'
            }
            
            for model_name, filename in model_files.items():
                model_path = os.path.join(self.models_dir, filename)
                if os.path.exists(model_path):
                    setattr(self, model_name, joblib.load(model_path))
                    self.logger.info(f"Loaded {model_name} from {model_path}")
                else:
                    self.logger.info(f"Model {model_name} not found, will use rule-based approach")
            
        except Exception as e:
            self.logger.warning(f"Error loading models: {e}")

# مثال على الاستخدام
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # إنشاء محرك الذكاء الاصطناعي
    ai_engine = AISecurityEngine()
    ai_engine.set_authorization(True, True)
    
    # مثال على تحليل شامل
    test_data = {
        "vulnerability_data": {
            "vulnerabilities": [
                {
                    "cve_id": "CVE-2023-0001",
                    "cvss_score": 9.8,
                    "exploit_available": True,
                    "published_date": "2023-01-01T00:00:00Z",
                    "description": "Remote code execution vulnerability"
                }
            ]
        },
        "exploit_data": {
            "target_type": "web_application",
            "defenses": ["firewall"],
            "attacker_skill": "intermediate",
            "exploit_complexity": "low"
        }
    }
    
    results = ai_engine.start_execution("comprehensive_analysis", **test_data)
    print(json.dumps(results, indent=2, ensure_ascii=False))
