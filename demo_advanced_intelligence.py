#!/usr/bin/env python3
"""
Demo script for Advanced Intelligence Module
سكربت تجريبي لوحدة الذكاء المتقدم

This script demonstrates the basic functionality of the advanced AI intelligence module.
هذا السكربت يوضح الوظائف الأساسية لوحدة الذكاء الاصطناعي المتقدم.
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_basic_ai_functionality():
    """Demo basic AI functionality"""
    print("\n" + "="*50)
    print("🧠 Basic AI Functionality Demo")
    print("عرض توضيحي للوظائف الأساسية للذكاء الاصطناعي")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        # Initialize AI system
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        # Collect behavioral data
        print("\n[*] Collecting behavioral data...")
        behavioral_data = ai_system.collect_behavioral_data()
        
        if behavioral_data:
            print(f"✅ Behavioral data collected successfully!")
            print(f"   CPU Usage: {behavioral_data.get('cpu_percent', 0):.2f}%")
            print(f"   Memory Usage: {behavioral_data.get('memory_percent', 0):.2f}%")
            print(f"   Active Processes: {behavioral_data.get('active_processes', 0)}")
            print(f"   User Activity Score: {behavioral_data.get('user_activity_score', 0):.2f}")
            
            return True
        else:
            print("❌ Failed to collect behavioral data")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure the advanced_intelligence module is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_threat_detection():
    """Demo threat detection capabilities"""
    print("\n" + "="*50)
    print("🚨 Threat Detection Demo")
    print("عرض توضيحي لاكتشاف التهديدات")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        # Collect threat indicators
        print("\n[*] Scanning for threat indicators...")
        threat_indicators = ai_system.collect_threat_indicators()
        
        if threat_indicators:
            print(f"✅ Found {len(threat_indicators)} threat indicators:")
            for indicator in threat_indicators[:3]:  # Show first 3
                print(f"   🔍 Type: {indicator.get('type', 'unknown')}")
                print(f"      Value: {indicator.get('value', 'N/A')}")
                print(f"      Confidence: {indicator.get('confidence', 0):.2f}")
                print(f"      Threat Level: {indicator.get('threat_level', 'unknown')}")
                print()
        else:
            print("ℹ️  No immediate threat indicators detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_ai_predictions():
    """Demo AI prediction capabilities"""
    print("\n" + "="*50)
    print("🔮 AI Predictions Demo")
    print("عرض توضيحي لتنبؤات الذكاء الاصطناعي")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        # Collect some data first
        print("\n[*] Collecting data for AI analysis...")
        for i in range(3):
            ai_system.collect_behavioral_data()
            ai_system.collect_threat_indicators()
            time.sleep(1)
        
        # Make predictions
        print("\n[*] Generating AI predictions...")
        predictions = ai_system.predict_threats()
        
        if predictions:
            print(f"✅ Generated {len(predictions)} AI predictions:")
            for prediction in predictions:
                print(f"   🤖 Type: {prediction.get('type', 'unknown')}")
                print(f"      Prediction: {prediction.get('value', 'N/A')}")
                print(f"      Confidence: {prediction.get('confidence', 0):.3f}")
                print(f"      Model: {prediction.get('model', 'unknown')}")
                print()
        else:
            print("ℹ️  No predictions generated (may need more data or ML libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_intelligent_decisions():
    """Demo intelligent decision making"""
    print("\n" + "="*50)
    print("⚡ Intelligent Decision Making Demo")
    print("عرض توضيحي لاتخاذ القرارات الذكية")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        # Create sample predictions for decision making
        sample_predictions = [
            {
                'type': 'anomaly_detection',
                'value': 'System anomaly detected (score: 0.75)',
                'confidence': 0.75,
                'model': 'IsolationForest',
                'timestamp': '2024-01-01T12:00:00'
            }
        ]
        
        print("\n[*] Making intelligent decisions based on AI analysis...")
        decisions = ai_system.make_intelligent_decisions(sample_predictions)
        
        if decisions:
            print(f"✅ Made {len(decisions)} intelligent decisions:")
            for decision in decisions:
                print(f"   ⚡ Action: {decision.get('action', 'unknown')}")
                print(f"      Reason: {decision.get('reason', 'N/A')}")
                print(f"      Priority: {decision.get('priority', 'unknown')}")
                print(f"      Timestamp: {decision.get('timestamp', 'N/A')}")
                print()
        else:
            print("ℹ️  No decisions made based on current analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_nlp_capabilities():
    """Demo Natural Language Processing capabilities"""
    print("\n" + "="*50)
    print("📝 Natural Language Processing Demo")
    print("عرض توضيحي لمعالجة اللغة الطبيعية")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        print("\n[*] Running NLP analysis on system data...")
        nlp_results = ai_system.natural_language_processing()
        
        if nlp_results:
            print(f"✅ NLP analysis completed with {len(nlp_results)} results:")
            for result in nlp_results[:2]:  # Show first 2
                print(f"   📄 Source: {result.get('source', 'unknown')}")
                print(f"      Text Length: {result.get('text_length', 0)}")
                print(f"      Classification: {result.get('classification', 'unknown')}")
                print(f"      Threat Keywords: {len(result.get('threat_keywords', []))}")
                print(f"      Sentiment Score: {result.get('sentiment_score', 0):.2f}")
                print()
        else:
            print("ℹ️  No NLP results (may require NLP libraries or text data)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_computer_vision():
    """Demo Computer Vision capabilities"""
    print("\n" + "="*50)
    print("👁️ Computer Vision Demo")
    print("عرض توضيحي لرؤية الكمبيوتر")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        print("\n[*] Running computer vision analysis...")
        cv_results = ai_system.computer_vision_analysis()
        
        if cv_results:
            print(f"✅ Computer vision analysis completed with {len(cv_results)} results:")
            for result in cv_results:
                print(f"   🖼️ Image Path: {result.get('image_path', 'unknown')}")
                print(f"      Extracted Text Length: {result.get('text_length', 0)}")
                print(f"      Security Indicators: {len(result.get('security_indicators', []))}")
                print(f"      Detected Objects: {len(result.get('detected_objects', []))}")
                print()
        else:
            print("ℹ️  No computer vision results (may require CV libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_comprehensive_ai():
    """Demo comprehensive AI system"""
    print("\n" + "="*50)
    print("🎯 Comprehensive AI System Demo")
    print("عرض توضيحي لنظام الذكاء الاصطناعي الشامل")
    print("="*50)
    
    try:
        from advanced_intelligence import RealAdvancedIntelligence
        
        ai_system = RealAdvancedIntelligence(output_dir="demo_ai_output")
        
        print("\n[*] Running comprehensive AI analysis...")
        
        # Generate intelligence report
        report = ai_system.generate_intelligence_report()
        
        if report:
            print(f"✅ Comprehensive AI analysis completed!")
            
            system_status = report.get('system_status', {})
            ai_models = report.get('ai_models_status', {})
            
            print(f"\n📊 System Status:")
            print(f"   Monitoring Active: {system_status.get('monitoring_active', False)}")
            print(f"   Learning Active: {system_status.get('learning_active', False)}")
            print(f"   Behavioral Data Points: {system_status.get('behavioral_data_points', 0)}")
            print(f"   Threat Indicators: {system_status.get('threat_indicators', 0)}")
            
            print(f"\n🤖 AI Models Status:")
            print(f"   Anomaly Detector: {'✅' if ai_models.get('anomaly_detector_trained') else '❌'}")
            print(f"   Threat Predictor: {'✅' if ai_models.get('threat_predictor_trained') else '❌'}")
            print(f"   Behavior Classifier: {'✅' if ai_models.get('behavior_classifier_trained') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main demo function"""
    print("🎭 Advanced Intelligence Module Demo")
    print("عرض توضيحي لوحدة الذكاء المتقدم")
    print("="*60)
    print("⚠️  This is a demonstration of AI-powered security capabilities")
    print("⚠️  هذا عرض توضيحي لقدرات الأمان المدعومة بالذكاء الاصطناعي")
    print("="*60)
    
    # Run demos
    demos = [
        ("Basic AI Functionality", demo_basic_ai_functionality),
        ("Threat Detection", demo_threat_detection),
        ("AI Predictions", demo_ai_predictions),
        ("Intelligent Decisions", demo_intelligent_decisions),
        ("NLP Capabilities", demo_nlp_capabilities),
        ("Computer Vision", demo_computer_vision),
        ("Comprehensive AI", demo_comprehensive_ai)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n🔄 Running {demo_name} demo...")
        try:
            results[demo_name] = demo_func()
        except Exception as e:
            print(f"❌ {demo_name} demo failed: {e}")
            results[demo_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEMO RESULTS SUMMARY")
    print("ملخص نتائج العرض التوضيحي")
    print("="*60)
    
    passed = 0
    total = len(demos)
    
    for demo_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} demos successful")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All demos completed successfully!")
        print("🎉 جميع العروض التوضيحية اكتملت بنجاح!")
        print("\nThe Advanced Intelligence module is working correctly.")
        print("وحدة الذكاء المتقدم تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} demo(s) failed.")
        print(f"⚠️  {total-passed} عرض توضيحي فشل.")
        print("\nSome features may require additional libraries.")
        print("بعض الميزات قد تتطلب مكتبات إضافية.")
    
    print(f"\n📁 Demo output saved to: demo_ai_output/")
    print(f"📁 مخرجات العرض التوضيحي محفوظة في: demo_ai_output/")

if __name__ == "__main__":
    main()
