# دليل البدء السريع
# Quick Start Guide

## 🚀 البدء السريع

### 1. التثبيت والإعداد

```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd security_exploitation_framework

# 2. إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/macOS
# أو
venv\Scripts\activate     # Windows

# 3. تشغيل نص الإعداد
python scripts/setup.py
```

### 2. التحقق من التثبيت

```bash
# عرض إحصائيات النظام
python main.py --stats

# تشغيل الاختبارات
python -m pytest tests/test_core.py -v
```

### 3. الاستخدام الأساسي

#### أ. الاستطلاع الأساسي

```bash
# استطلاع موقع ويب
python main.py --recon example.com

# استطلاع عنوان IP
python main.py --recon ***********
```

#### ب. توليد البيانات الحقيقية

```bash
# جلب ثغرات حقيقية من آخر 7 أيام
python main.py --generate-data vulnerabilities --days-back 7

# توليد حركة مرور شبكة لـ 24 ساعة
python main.py --generate-data network_traffic --duration-hours 24

# توليد سلوك مستخدمين (50 مستخدم لـ 7 أيام)
python main.py --generate-data user_behavior --num-users 50 --days 7

# توليد سيناريوهات هجوم
python main.py --generate-data attack_scenarios
```

## 🔧 الاستخدام المتقدم

### 1. استخدام Python API

```python
from core.config_manager import config
from core.real_data_generator import RealDataGenerator
from modules.reconnaissance.basic_recon import BasicReconModule

# تحميل الإعدادات
config.load_config('config.yaml')

# إنشاء مولد البيانات الحقيقية
data_generator = RealDataGenerator()

# جلب ثغرات حقيقية
vulnerabilities = data_generator.fetch_real_vulnerabilities(days_back=30)
print(f"Found {len(vulnerabilities)} vulnerabilities")

# إنشاء وحدة استطلاع
recon_module = BasicReconModule()
recon_module.set_authorization(True, True)

# تنفيذ الاستطلاع
results = recon_module.start_execution("example.com")
print(f"Reconnaissance completed: {results['success']}")
```

### 2. إعداد مفاتيح API (اختياري)

قم بتحرير `config.yaml` وإضافة مفاتيح API:

```yaml
real_data:
  sources:
    shodan:
      api_key: "YOUR_SHODAN_API_KEY"
      enabled: true
    virustotal:
      api_key: "YOUR_VIRUSTOTAL_API_KEY"
      enabled: true
```

### 3. إنشاء وحدة مخصصة

```python
from core.base_module import BaseSecurityModule, ModuleType

class CustomModule(BaseSecurityModule):
    def __init__(self):
        super().__init__("custom_module", ModuleType.RECONNAISSANCE)
    
    def validate_target(self, target: str) -> bool:
        # تنفيذ التحقق من صحة الهدف
        return True
    
    def execute(self, target: str, **kwargs):
        # تنفيذ منطق الوحدة
        return {
            "success": True,
            "target": target,
            "data": "custom_results"
        }

# استخدام الوحدة
module = CustomModule()
module.set_authorization(True, True)
results = module.start_execution("target.com")
```

## 📊 مراقبة النظام

### عرض الإحصائيات

```bash
python main.py --stats
```

### فحص السجلات

```bash
# سجلات النظام
tail -f logs/framework.log

# سجلات الأمان
tail -f logs/security.log
```

### فحص قاعدة البيانات

```python
from core.database_manager import db_manager

# إحصائيات قاعدة البيانات
stats = db_manager.get_database_statistics()
print(stats)

# الحصول على الثغرات
vulnerabilities = db_manager.get_vulnerabilities(limit=10, severity_min=7.0)
for vuln in vulnerabilities:
    print(f"{vuln['cve_id']}: {vuln['cvss_score']}")
```

## ⚠️ تذكيرات مهمة

### 1. الاستخدام القانوني

- **احصل على تصريح كتابي** قبل اختبار أي نظام
- **اتبع القوانين المحلية** والدولية
- **استخدم فقط في البيئات المصرح بها**

### 2. الأمان

- **لا تشارك مفاتيح API** أو كلمات المرور
- **استخدم شبكات آمنة** للاختبار
- **احتفظ بنسخ احتياطية** من البيانات المهمة

### 3. الأداء

- **راقب استخدام الموارد** أثناء العمليات الكبيرة
- **استخدم حدود معقولة** للطلبات والمهلة الزمنية
- **نظف البيانات المؤقتة** بانتظام

## 🆘 استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في تحميل الإعدادات

```bash
# التحقق من وجود ملف الإعدادات
ls -la config.yaml

# التحقق من صحة YAML
python -c "import yaml; yaml.safe_load(open('config.yaml'))"
```

#### 2. مشاكل قاعدة البيانات

```bash
# إعادة تهيئة قاعدة البيانات
rm -f data/databases/framework.db
python -c "from core.database_manager import db_manager; print('Database reinitialized')"
```

#### 3. مشاكل الأذونات

```bash
# إصلاح أذونات مجلد المفاتيح
chmod 700 data/keys/
chmod 600 data/keys/*
```

#### 4. مشاكل الشبكة

```bash
# اختبار الاتصال بالإنترنت
curl -I https://services.nvd.nist.gov/rest/json/cves/1.0

# اختبار DNS
nslookup example.com
```

### تشغيل التشخيص

```python
from core.config_manager import config
from core.database_manager import db_manager
from core.security_manager import security_manager

# فحص الإعدادات
validation = config.validate_config()
print("Config validation:", validation)

# فحص قاعدة البيانات
db_stats = db_manager.get_database_statistics()
print("Database stats:", db_stats)

# فحص الأمان
security_stats = security_manager.get_security_statistics()
print("Security stats:", security_stats)
```

## 📚 موارد إضافية

- **التوثيق الكامل**: `docs/`
- **أمثلة متقدمة**: `examples/`
- **اختبارات**: `tests/`
- **قوالب**: `templates/`

## 🤝 الحصول على المساعدة

1. **راجع السجلات**: `logs/framework.log`
2. **تشغيل الاختبارات**: `python -m pytest tests/ -v`
3. **فحص الإعدادات**: `python main.py --stats`
4. **مراجعة التوثيق**: `docs/`

---

**تذكير**: استخدم هذا الإطار بمسؤولية وفقط للأغراض المصرح بها قانونياً.
