# Security Exploitation Framework Configuration
# إعدادات إطار عمل استغلال الأمان

# إعدادات عامة
general:
  framework_name: "Security Exploitation Framework"
  version: "1.0.0"
  debug_mode: true
  log_level: "INFO"
  max_concurrent_sessions: 10
  session_timeout: 3600  # seconds

# إعدادات قاعدة البيانات
database:
  type: "sqlite"  # sqlite, postgresql, mongodb
  sqlite:
    path: "data/databases/framework.db"
  postgresql:
    host: "localhost"
    port: 5432
    database: "security_framework"
    username: "framework_user"
    password: "secure_password"
  mongodb:
    host: "localhost"
    port: 27017
    database: "security_framework"

# إعدادات التسجيل
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/framework.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# إعدادات الأمان
security:
  encryption_key_path: "data/keys/master.key"
  session_encryption: true
  api_authentication: true
  rate_limiting: true
  max_requests_per_minute: 100

# إعدادات البيانات الحقيقية
real_data:
  enabled: true
  sources:
    cve_database:
      url: "https://services.nvd.nist.gov/rest/json/cves/1.0"
      update_interval: 86400  # seconds (24 hours)
    threat_feeds:
      - "https://otx.alienvault.com/api/v1/indicators/export"
      - "https://api.abuse.ch/api/v2/"
      - "https://urlhaus-api.abuse.ch/v1/"
    shodan:
      api_key: ""  # يجب إضافة مفتاح API
      enabled: false
    virustotal:
      api_key: ""  # يجب إضافة مفتاح API
      enabled: false

# إعدادات بيئة المختبر الافتراضي
virtual_lab:
  enabled: true
  type: "vmware"  # vmware, virtualbox, docker
  networks:
    corporate:
      subnet: "192.168.100.0/24"
      gateway: "192.168.100.1"
    dmz:
      subnet: "10.0.1.0/24"
      gateway: "10.0.1.1"
    iot:
      subnet: "172.16.0.0/24"
      gateway: "172.16.0.1"

# إعدادات الوحدات
modules:
  reconnaissance:
    enabled: true
    timeout: 300
    max_threads: 5
  exploitation:
    enabled: true
    timeout: 600
    safety_checks: true
  post_exploitation:
    enabled: true
    data_collection_limit: "100MB"
  persistence:
    enabled: false  # يتطلب تصريح خاص
  evasion:
    enabled: true
    techniques: ["obfuscation", "encryption", "polymorphism"]

# إعدادات الذكاء الاصطناعي
ai_engine:
  enabled: true
  model_path: "data/models/"
  learning_rate: 0.001
  batch_size: 32
  max_training_epochs: 100

# إعدادات واجهة المستخدم
ui:
  web_interface:
    enabled: true
    host: "127.0.0.1"
    port: 8080
    ssl_enabled: false
  gui:
    enabled: true
    theme: "dark"

# إعدادات API
api:
  enabled: true
  host: "127.0.0.1"
  port: 8000
  cors_enabled: true
  rate_limiting: true

# إعدادات التقارير
reporting:
  output_directory: "reports/"
  formats: ["html", "pdf", "json"]
  include_screenshots: true
  auto_generate: true

# إعدادات الامتثال والقانون
compliance:
  require_authorization: true
  log_all_activities: true
  data_retention_days: 90
  anonymize_sensitive_data: true

# إعدادات التحديث
updates:
  auto_update: false
  check_interval: 86400  # seconds
  update_sources:
    - "https://updates.framework.local/vulnerabilities"
    - "https://updates.framework.local/exploits"
    - "https://updates.framework.local/signatures"
