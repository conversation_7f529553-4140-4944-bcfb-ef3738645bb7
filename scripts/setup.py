#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نص إعداد إطار عمل استغلال الأمان
Security Exploitation Framework Setup Script
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import yaml
import json

class FrameworkSetup:
    """فئة إعداد الإطار"""
    
    def __init__(self):
        """تهيئة نص الإعداد"""
        self.setup_logger()
        self.framework_root = Path(__file__).parent.parent
        self.config_file = self.framework_root / "config.yaml"
        self.requirements_file = self.framework_root / "requirements.txt"
        
        self.logger.info("Framework Setup initialized")
    
    def setup_logger(self):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('setup.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_python_version(self) -> bool:
        """
        فحص إصدار Python
        
        Returns:
            bool: True إذا كان الإصدار مدعوم
        """
        min_version = (3, 8)
        current_version = sys.version_info[:2]
        
        if current_version >= min_version:
            self.logger.info(f"Python version {sys.version} is supported")
            return True
        else:
            self.logger.error(f"Python {min_version[0]}.{min_version[1]}+ required, found {current_version[0]}.{current_version[1]}")
            return False
    
    def create_directories(self) -> bool:
        """
        إنشاء المجلدات المطلوبة
        
        Returns:
            bool: True إذا تم الإنشاء بنجاح
        """
        try:
            directories = [
                "data/databases",
                "data/cache",
                "data/keys",
                "data/wordlists",
                "data/signatures",
                "data/intelligence",
                "logs",
                "reports",
                "templates/reports",
                "templates/configs",
                "templates/workflows"
            ]
            
            for directory in directories:
                dir_path = self.framework_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"Created directory: {directory}")
            
            # تعيين صلاحيات آمنة لمجلد المفاتيح
            keys_dir = self.framework_root / "data/keys"
            if os.name != 'nt':  # ليس Windows
                os.chmod(keys_dir, 0o700)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating directories: {e}")
            return False
    
    def install_requirements(self) -> bool:
        """
        تثبيت المتطلبات
        
        Returns:
            bool: True إذا تم التثبيت بنجاح
        """
        try:
            if not self.requirements_file.exists():
                self.logger.error("requirements.txt not found")
                return False
            
            self.logger.info("Installing Python requirements...")
            
            # تحديث pip أولاً
            subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True)
            
            # تثبيت المتطلبات
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
            ], check=True, capture_output=True)
            
            self.logger.info("Requirements installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error installing requirements: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during installation: {e}")
            return False
    
    def initialize_database(self) -> bool:
        """
        تهيئة قاعدة البيانات
        
        Returns:
            bool: True إذا تم التهيئة بنجاح
        """
        try:
            self.logger.info("Initializing database...")
            
            # إضافة مسار المشروع إلى Python path
            sys.path.insert(0, str(self.framework_root))
            
            from core.database_manager import db_manager
            
            # إنشاء قاعدة البيانات والجداول
            stats = db_manager.get_database_statistics()
            self.logger.info(f"Database initialized with {len(stats)} tables")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            return False
    
    def setup_encryption(self) -> bool:
        """
        إعداد التشفير
        
        Returns:
            bool: True إذا تم الإعداد بنجاح
        """
        try:
            self.logger.info("Setting up encryption...")
            
            # إضافة مسار المشروع إلى Python path
            sys.path.insert(0, str(self.framework_root))
            
            from core.security_manager import security_manager
            
            # التحقق من إعداد التشفير
            stats = security_manager.get_security_statistics()
            if stats['master_key_exists']:
                self.logger.info("Encryption setup completed")
                return True
            else:
                self.logger.error("Failed to setup encryption")
                return False
            
        except Exception as e:
            self.logger.error(f"Error setting up encryption: {e}")
            return False
    
    def create_default_config(self) -> bool:
        """
        إنشاء ملف الإعدادات الافتراضي
        
        Returns:
            bool: True إذا تم الإنشاء بنجاح
        """
        try:
            if self.config_file.exists():
                self.logger.info("Configuration file already exists")
                return True
            
            default_config = {
                "general": {
                    "framework_name": "Security Exploitation Framework",
                    "version": "1.0.0",
                    "debug_mode": False,
                    "log_level": "INFO",
                    "max_concurrent_sessions": 10,
                    "session_timeout": 3600
                },
                "database": {
                    "type": "sqlite",
                    "sqlite": {
                        "path": "data/databases/framework.db"
                    }
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "file_path": "logs/framework.log",
                    "max_file_size": "10MB",
                    "backup_count": 5,
                    "console_output": True
                },
                "security": {
                    "encryption_key_path": "data/keys/master.key",
                    "session_encryption": True,
                    "api_authentication": True,
                    "rate_limiting": True,
                    "max_requests_per_minute": 100
                },
                "real_data": {
                    "enabled": True,
                    "sources": {
                        "cve_database": {
                            "url": "https://services.nvd.nist.gov/rest/json/cves/1.0",
                            "update_interval": 86400
                        },
                        "shodan": {
                            "api_key": "",
                            "enabled": False
                        },
                        "virustotal": {
                            "api_key": "",
                            "enabled": False
                        }
                    }
                },
                "modules": {
                    "reconnaissance": {
                        "enabled": True,
                        "timeout": 300,
                        "max_threads": 5
                    },
                    "exploitation": {
                        "enabled": True,
                        "timeout": 600,
                        "safety_checks": True
                    }
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.logger.info("Default configuration file created")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating configuration file: {e}")
            return False
    
    def run_tests(self) -> bool:
        """
        تشغيل الاختبارات الأساسية
        
        Returns:
            bool: True إذا نجحت الاختبارات
        """
        try:
            self.logger.info("Running basic tests...")
            
            # اختبار استيراد الوحدات الأساسية
            sys.path.insert(0, str(self.framework_root))
            
            from core.config_manager import config
            from core.database_manager import db_manager
            from core.security_manager import security_manager
            from core.real_data_generator import RealDataGenerator
            
            # اختبار تحميل الإعدادات
            if config.load_config(str(self.config_file)):
                self.logger.info("✓ Configuration loading test passed")
            else:
                self.logger.error("✗ Configuration loading test failed")
                return False
            
            # اختبار قاعدة البيانات
            stats = db_manager.get_database_statistics()
            if isinstance(stats, dict):
                self.logger.info("✓ Database test passed")
            else:
                self.logger.error("✗ Database test failed")
                return False
            
            # اختبار الأمان
            security_stats = security_manager.get_security_statistics()
            if security_stats['master_key_exists']:
                self.logger.info("✓ Security test passed")
            else:
                self.logger.error("✗ Security test failed")
                return False
            
            # اختبار مولد البيانات
            data_generator = RealDataGenerator()
            if data_generator:
                self.logger.info("✓ Data generator test passed")
            else:
                self.logger.error("✗ Data generator test failed")
                return False
            
            self.logger.info("All basic tests passed!")
            return True
            
        except Exception as e:
            self.logger.error(f"Error running tests: {e}")
            return False
    
    def display_setup_summary(self):
        """عرض ملخص الإعداد"""
        print("\n" + "="*60)
        print("🎯 Security Exploitation Framework Setup Complete!")
        print("="*60)
        print("\n📋 Setup Summary:")
        print("  ✓ Python version check")
        print("  ✓ Directory structure created")
        print("  ✓ Requirements installed")
        print("  ✓ Database initialized")
        print("  ✓ Encryption configured")
        print("  ✓ Configuration file created")
        print("  ✓ Basic tests passed")
        
        print("\n🚀 Next Steps:")
        print("  1. Review and customize config.yaml")
        print("  2. Add API keys for external services (optional)")
        print("  3. Run: python -c 'from core.real_data_generator import RealDataGenerator; RealDataGenerator().fetch_real_vulnerabilities()'")
        print("  4. Check the documentation in docs/")
        
        print("\n⚠️  Important Reminders:")
        print("  • Use only for authorized security testing")
        print("  • Follow all applicable laws and regulations")
        print("  • Obtain proper authorization before testing")
        
        print("\n📞 Support:")
        print("  • Documentation: docs/")
        print("  • Issues: GitHub Issues")
        print("  • Logs: setup.log")
        print("\n" + "="*60)
    
    def run_setup(self) -> bool:
        """
        تشغيل عملية الإعداد الكاملة
        
        Returns:
            bool: True إذا تم الإعداد بنجاح
        """
        self.logger.info("Starting Security Exploitation Framework setup...")
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Creating directories", self.create_directories),
            ("Installing requirements", self.install_requirements),
            ("Creating configuration", self.create_default_config),
            ("Initializing database", self.initialize_database),
            ("Setting up encryption", self.setup_encryption),
            ("Running tests", self.run_tests)
        ]
        
        for step_name, step_function in steps:
            self.logger.info(f"Step: {step_name}")
            if not step_function():
                self.logger.error(f"Setup failed at step: {step_name}")
                return False
        
        self.display_setup_summary()
        return True

def main():
    """الدالة الرئيسية"""
    setup = FrameworkSetup()
    
    try:
        success = setup.run_setup()
        if success:
            print("\n🎉 Setup completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Setup failed. Check setup.log for details.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Setup interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
