#!/usr/bin/env python3
"""
Test script for Advanced Propagation Module
سكربت اختبار لوحدة الانتشار المتقدم

This script tests the advanced propagation capabilities in a safe manner.
هذا السكربت يختبر قدرات الانتشار المتقدم بطريقة آمنة.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from advanced_propagation import RealAdvancedPropagation
except ImportError as e:
    print(f"[-] Error importing advanced_propagation module: {e}")
    sys.exit(1)


def test_network_discovery():
    """Test network discovery capabilities"""
    print("\n" + "="*50)
    print("🔍 Testing Network Discovery")
    print("اختبار اكتشاف الشبكة")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        print("\n[*] Discovering network ranges...")
        network_ranges = propagation.discover_network_ranges()
        
        if network_ranges:
            print(f"✅ Network discovery successful!")
            print(f"   Found {len(network_ranges)} network ranges:")
            
            for network in network_ranges[:3]:  # Show first 3
                print(f"   📡 Interface: {network.get('interface', 'unknown')}")
                print(f"      Network: {network.get('network', 'unknown')}")
                print(f"      IP: {network.get('ip', 'unknown')}")
                print(f"      Hosts: {network.get('hosts_count', 0)}")
                print()
        else:
            print("ℹ️  No network ranges discovered")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_port_scanning():
    """Test port scanning capabilities"""
    print("\n" + "="*50)
    print("🎯 Testing Port Scanning")
    print("اختبار فحص المنافذ")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        # Test safe targets
        test_targets = ['127.0.0.1', '*******']  # Localhost and Google DNS
        
        for target in test_targets:
            print(f"\n[*] Scanning {target}...")
            
            # Scan common ports only for safety
            common_ports = [22, 53, 80, 443]
            open_ports, services = propagation.advanced_port_scan(target, common_ports)
            
            if open_ports:
                print(f"✅ Port scan successful for {target}!")
                print(f"   Open ports: {open_ports}")
                
                for port in open_ports:
                    service_info = services.get(port, {})
                    service_name = service_info.get('name', 'unknown')
                    print(f"   🔓 Port {port}: {service_name}")
            else:
                print(f"ℹ️  No open ports found on {target} (or filtered)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_vulnerability_assessment():
    """Test vulnerability assessment"""
    print("\n" + "="*50)
    print("🔍 Testing Vulnerability Assessment")
    print("اختبار تقييم الثغرات")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        # Test vulnerability scoring
        test_cases = [
            ([22, 80, 443], {'22': {'name': 'ssh'}, '80': {'name': 'http'}}),
            ([21, 23, 135, 445], {'21': {'name': 'ftp'}, '23': {'name': 'telnet'}}),
            ([3389, 1433, 3306], {'3389': {'name': 'rdp'}, '1433': {'name': 'mssql'}})
        ]
        
        for i, (ports, services) in enumerate(test_cases):
            score = propagation.calculate_vulnerability_score(ports, services)
            print(f"✅ Test case {i+1}:")
            print(f"   Ports: {ports}")
            print(f"   Vulnerability Score: {score:.2f}/10.0")
            
            if score > 5.0:
                print(f"   ⚠️  High vulnerability score!")
            elif score > 2.0:
                print(f"   ⚠️  Medium vulnerability score")
            else:
                print(f"   ℹ️  Low vulnerability score")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_exploitation_simulation():
    """Test exploitation capabilities (simulation only)"""
    print("\n" + "="*50)
    print("🎯 Testing Exploitation Simulation")
    print("اختبار محاكاة الاستغلال")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        # Test against non-existent targets for safety
        test_targets = ['***************', '10.255.255.254']  # Unlikely to exist
        
        for target in test_targets:
            print(f"\n[*] Testing exploitation simulation on {target}...")
            
            # Test SSH brute force (will fail safely)
            print("   Testing SSH brute force...")
            success, username, password = propagation.ssh_bruteforce_attack(target, 22)
            
            if success:
                print(f"   ⚠️  SSH brute force succeeded: {username}:{password}")
            else:
                print(f"   ✅ SSH brute force failed (expected for test)")
            
            # Test FTP brute force (will fail safely)
            print("   Testing FTP brute force...")
            success, username, password = propagation.ftp_bruteforce_attack(target, 21)
            
            if success:
                print(f"   ⚠️  FTP brute force succeeded: {username}:{password}")
            else:
                print(f"   ✅ FTP brute force failed (expected for test)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_payload_generation():
    """Test payload generation"""
    print("\n" + "="*50)
    print("💾 Testing Payload Generation")
    print("اختبار إنشاء الحمولة")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        print("\n[*] Generating payload content...")
        payload_content = propagation.generate_payload_content()
        
        if payload_content:
            print(f"✅ Payload generation successful!")
            print(f"   Payload size: {len(payload_content)} bytes")
            print(f"   Contains Python code: {'python' in payload_content.lower()}")
            print(f"   Contains main function: {'def main()' in payload_content}")
        
        print("\n[*] Generating persistence commands...")
        persistence_commands = propagation.generate_persistence_commands()
        
        if persistence_commands:
            print(f"✅ Persistence commands generated!")
            print(f"   Total commands: {len(persistence_commands)}")
            print(f"   Contains cron: {any('cron' in cmd for cmd in persistence_commands)}")
            print(f"   Contains systemd: {any('systemd' in cmd for cmd in persistence_commands)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_database_operations():
    """Test database operations"""
    print("\n" + "="*50)
    print("🗄️ Testing Database Operations")
    print("اختبار عمليات قاعدة البيانات")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        # Test storing discovered host
        print("\n[*] Testing host storage...")
        test_host_data = {
            'ip_address': '*************',
            'hostname': 'test-host',
            'os_type': 'Linux',
            'open_ports': '[22, 80, 443]',
            'services': '{"22": {"name": "ssh"}}',
            'vulnerability_score': 3.5,
            'scan_method': 'manual'
        }
        
        propagation.store_discovered_host(test_host_data)
        print("✅ Host data stored successfully")
        
        # Test storing exploitation attempt
        print("\n[*] Testing exploitation attempt storage...")
        propagation.store_exploitation_attempt(
            '*************', 22, 'ssh', 'bruteforce',
            'admin:password', False, 'Connection refused', 2.5
        )
        print("✅ Exploitation attempt stored successfully")
        
        # Test storing compromised host
        print("\n[*] Testing compromised host storage...")
        propagation.store_compromised_host(
            '*************', 'ssh', 'admin:password'
        )
        print("✅ Compromised host stored successfully")
        
        # Test report generation
        print("\n[*] Testing report generation...")
        report = propagation.generate_propagation_report()
        
        if report:
            print("✅ Report generated successfully!")
            print(f"   Report timestamp: {report.get('timestamp', 'N/A')}")
            print(f"   Statistics: {report.get('statistics', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_lateral_movement_discovery():
    """Test lateral movement target discovery"""
    print("\n" + "="*50)
    print("🔄 Testing Lateral Movement Discovery")
    print("اختبار اكتشاف أهداف الحركة الجانبية")
    print("="*50)
    
    try:
        propagation = RealAdvancedPropagation(output_dir="test_propagation_output")
        
        # Test with non-existent host for safety
        test_ip = '***************'
        test_credentials = 'admin:password'
        
        print(f"\n[*] Testing lateral movement discovery from {test_ip}...")
        new_targets = propagation.discover_lateral_movement_targets(
            test_ip, 'ssh', test_credentials
        )
        
        print(f"✅ Lateral movement discovery completed")
        print(f"   Discovered targets: {len(new_targets)}")
        
        if new_targets:
            print(f"   Sample targets: {new_targets[:5]}")
        else:
            print(f"   No targets discovered (expected for test)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Advanced Propagation Module Test Suite")
    print("مجموعة اختبارات وحدة الانتشار المتقدم")
    print("="*60)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*60)
    print("⚠️  These tests use safe targets and simulation")
    print("⚠️  هذه الاختبارات تستخدم أهداف آمنة ومحاكاة")
    print("="*60)
    
    # Run tests
    tests = [
        ("Network Discovery", test_network_discovery),
        ("Port Scanning", test_port_scanning),
        ("Vulnerability Assessment", test_vulnerability_assessment),
        ("Exploitation Simulation", test_exploitation_simulation),
        ("Payload Generation", test_payload_generation),
        ("Database Operations", test_database_operations),
        ("Lateral Movement Discovery", test_lateral_movement_discovery)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[-] {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("ملخص نتائج الاختبارات")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Advanced Propagation module is working correctly.")
        print("🎉 جميع الاختبارات نجحت! وحدة الانتشار المتقدم تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Check the errors above.")
        print(f"⚠️  {total-passed} اختبار فشل. تحقق من الأخطاء أعلاه.")
    
    print(f"\n📁 Test output saved to: test_propagation_output/")
    print(f"📁 مخرجات الاختبار محفوظة في: test_propagation_output/")
    
    print(f"\n⚠️  IMPORTANT REMINDER:")
    print(f"⚠️  تذكير مهم:")
    print(f"   - Use only in authorized environments")
    print(f"   - استخدم فقط في البيئات المصرح بها")
    print(f"   - Real exploitation requires proper authorization")
    print(f"   - الاستغلال الحقيقي يتطلب تصريح مناسب")
    
    return results


if __name__ == "__main__":
    main()
