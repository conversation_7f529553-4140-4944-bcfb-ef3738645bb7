#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قواعد البيانات المركزي
Central Database Manager
"""

import sqlite3
import logging
import json
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from contextlib import contextmanager
import uuid

class DatabaseManager:
    """مدير قواعد البيانات المركزي"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """تهيئة مدير قواعد البيانات"""
        if hasattr(self, 'initialized'):
            return

        self.initialized = True
        self.logger = logging.getLogger(__name__)
        self.db_path = None
        self.connection_pool = {}
        self.schema_version = "1.0.0"

        # إعداد قاعدة البيانات الافتراضية
        self._setup_default_database()

    def _setup_default_database(self):
        """إعداد قاعدة البيانات الافتراضية"""
        # إنشاء مجلد قواعد البيانات
        db_dir = Path("data/databases")
        db_dir.mkdir(parents=True, exist_ok=True)

        # تعيين مسار قاعدة البيانات
        self.db_path = db_dir / "framework.db"

        # إنشاء الجداول الأساسية
        self._create_tables()

        self.logger.info(f"Database initialized at: {self.db_path}")

    @contextmanager
    def get_connection(self):
        """
        الحصول على اتصال قاعدة البيانات مع إدارة السياق

        Yields:
            sqlite3.Connection: اتصال قاعدة البيانات
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def _create_tables(self):
        """إنشاء الجداول الأساسية"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # جدول الجلسات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    data TEXT,
                    ip_address TEXT,
                    user_agent TEXT
                )
            ''')

            # جدول الحملات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS campaigns (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'created',
                    targets TEXT,
                    modules TEXT,
                    results TEXT,
                    completed_at TIMESTAMP
                )
            ''')

            # جدول نتائج الوحدات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS module_results (
                    id TEXT PRIMARY KEY,
                    session_id TEXT,
                    campaign_id TEXT,
                    module_name TEXT NOT NULL,
                    module_type TEXT,
                    target TEXT,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    status TEXT DEFAULT 'running',
                    results TEXT,
                    errors TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (campaign_id) REFERENCES campaigns (id)
                )
            ''')

            # جدول الثغرات الحقيقية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_vulnerabilities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cve_id TEXT UNIQUE,
                    description TEXT,
                    severity REAL,
                    cvss_score REAL,
                    published_date TEXT,
                    modified_date TEXT,
                    affected_products TEXT,
                    exploit_available BOOLEAN DEFAULT 0,
                    exploit_code TEXT,
                    reference_urls TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول التهديدات الحقيقية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_threats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    threat_type TEXT,
                    indicator_value TEXT,
                    indicator_type TEXT,
                    confidence_score REAL,
                    source TEXT,
                    first_seen TEXT,
                    last_seen TEXT,
                    tags TEXT,
                    context TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول بيانات الشبكة الحقيقية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_network_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ip_address TEXT,
                    hostname TEXT,
                    open_ports TEXT,
                    services TEXT,
                    os_info TEXT,
                    geolocation TEXT,
                    asn_info TEXT,
                    scan_timestamp TEXT,
                    data_source TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول السجلات الأمنية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    event_type TEXT,
                    user_id TEXT,
                    session_id TEXT,
                    level TEXT DEFAULT 'INFO',
                    message TEXT,
                    details TEXT
                )
            ''')

            # جدول إعدادات النظام
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إنشاء الفهارس
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_campaigns_created_by ON campaigns(created_by)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_module_results_session ON module_results(session_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_vulnerabilities_cve ON real_vulnerabilities(cve_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_threats_type ON real_threats(threat_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_network_ip ON real_network_data(ip_address)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_security_logs_event ON security_logs(event_type)')

            conn.commit()
            self.logger.info("Database tables created successfully")

    def create_session(self, user_id: str, ip_address: str = None,
                      user_agent: str = None, data: Dict = None) -> str:
        """
        إنشاء جلسة جديدة

        Args:
            user_id: معرف المستخدم
            ip_address: عنوان IP
            user_agent: معلومات المتصفح
            data: بيانات إضافية

        Returns:
            str: معرف الجلسة
        """
        session_id = str(uuid.uuid4())

        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO sessions (id, user_id, ip_address, user_agent, data)
                VALUES (?, ?, ?, ?, ?)
            ''', (session_id, user_id, ip_address, user_agent,
                  json.dumps(data) if data else None))
            conn.commit()

        self.logger.info(f"Session created: {session_id} for user: {user_id}")
        return session_id

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات الجلسة

        Args:
            session_id: معرف الجلسة

        Returns:
            Dict: معلومات الجلسة أو None
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM sessions WHERE id = ?', (session_id,))
            row = cursor.fetchone()

            if row:
                session_data = dict(row)
                if session_data['data']:
                    session_data['data'] = json.loads(session_data['data'])
                return session_data

        return None

    def update_session_activity(self, session_id: str):
        """
        تحديث نشاط الجلسة

        Args:
            session_id: معرف الجلسة
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE sessions
                SET last_activity = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (session_id,))
            conn.commit()

    def close_session(self, session_id: str):
        """
        إغلاق الجلسة

        Args:
            session_id: معرف الجلسة
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE sessions
                SET status = 'closed'
                WHERE id = ?
            ''', (session_id,))
            conn.commit()

        self.logger.info(f"Session closed: {session_id}")

    def create_campaign(self, name: str, description: str, created_by: str,
                       targets: List[str] = None, modules: List[str] = None) -> str:
        """
        إنشاء حملة جديدة

        Args:
            name: اسم الحملة
            description: وصف الحملة
            created_by: منشئ الحملة
            targets: قائمة الأهداف
            modules: قائمة الوحدات

        Returns:
            str: معرف الحملة
        """
        campaign_id = str(uuid.uuid4())

        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO campaigns (id, name, description, created_by, targets, modules)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (campaign_id, name, description, created_by,
                  json.dumps(targets) if targets else None,
                  json.dumps(modules) if modules else None))
            conn.commit()

        self.logger.info(f"Campaign created: {campaign_id} - {name}")
        return campaign_id

    def save_module_result(self, session_id: str, module_name: str,
                          module_type: str, target: str, results: Dict[str, Any],
                          campaign_id: str = None, errors: List[str] = None) -> str:
        """
        حفظ نتائج الوحدة

        Args:
            session_id: معرف الجلسة
            module_name: اسم الوحدة
            module_type: نوع الوحدة
            target: الهدف
            results: النتائج
            campaign_id: معرف الحملة
            errors: قائمة الأخطاء

        Returns:
            str: معرف النتيجة
        """
        result_id = str(uuid.uuid4())

        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO module_results
                (id, session_id, campaign_id, module_name, module_type, target,
                 completed_at, status, results, errors)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?)
            ''', (result_id, session_id, campaign_id, module_name, module_type,
                  target, 'completed', json.dumps(results),
                  json.dumps(errors) if errors else None))
            conn.commit()

        return result_id

    def store_vulnerability(self, vuln_data: Dict[str, Any]) -> bool:
        """
        حفظ بيانات الثغرة

        Args:
            vuln_data: بيانات الثغرة

        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO real_vulnerabilities
                    (cve_id, description, severity, cvss_score, published_date,
                     modified_date, affected_products, exploit_available,
                     exploit_code, reference_urls)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    vuln_data.get('cve_id'),
                    vuln_data.get('description'),
                    vuln_data.get('severity'),
                    vuln_data.get('cvss_score'),
                    vuln_data.get('published_date'),
                    vuln_data.get('modified_date'),
                    json.dumps(vuln_data.get('affected_products')),
                    vuln_data.get('exploit_available', False),
                    vuln_data.get('exploit_code'),
                    json.dumps(vuln_data.get('references'))
                ))
                conn.commit()
            return True
        except Exception as e:
            self.logger.error(f"Error storing vulnerability: {e}")
            return False

    def store_threat(self, threat_data: Dict[str, Any]) -> bool:
        """
        حفظ بيانات التهديد

        Args:
            threat_data: بيانات التهديد

        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO real_threats
                    (threat_type, indicator_value, indicator_type, confidence_score,
                     source, first_seen, last_seen, tags, context)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    threat_data.get('threat_type'),
                    threat_data.get('indicator_value'),
                    threat_data.get('indicator_type'),
                    threat_data.get('confidence_score'),
                    threat_data.get('source'),
                    threat_data.get('first_seen'),
                    threat_data.get('last_seen'),
                    json.dumps(threat_data.get('tags')),
                    json.dumps(threat_data.get('context'))
                ))
                conn.commit()
            return True
        except Exception as e:
            self.logger.error(f"Error storing threat: {e}")
            return False

    def get_vulnerabilities(self, limit: int = 100, severity_min: float = None) -> List[Dict]:
        """
        الحصول على قائمة الثغرات

        Args:
            limit: عدد النتائج المحدود
            severity_min: الحد الأدنى للخطورة

        Returns:
            List[Dict]: قائمة الثغرات
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = 'SELECT * FROM real_vulnerabilities'
            params = []

            if severity_min is not None:
                query += ' WHERE cvss_score >= ?'
                params.append(severity_min)

            query += ' ORDER BY cvss_score DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            vulnerabilities = []
            for row in rows:
                vuln = dict(row)
                if vuln['affected_products']:
                    vuln['affected_products'] = json.loads(vuln['affected_products'])
                if vuln['reference_urls']:
                    vuln['reference_urls'] = json.loads(vuln['reference_urls'])
                vulnerabilities.append(vuln)

            return vulnerabilities

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات قاعدة البيانات

        Returns:
            Dict: إحصائيات قاعدة البيانات
        """
        stats = {}

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # إحصائيات الجداول
            tables = [
                'sessions', 'campaigns', 'module_results',
                'real_vulnerabilities', 'real_threats',
                'real_network_data', 'security_logs'
            ]

            for table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                stats[f'{table}_count'] = cursor.fetchone()[0]

            # حجم قاعدة البيانات
            if self.db_path.exists():
                stats['database_size_bytes'] = self.db_path.stat().st_size
                stats['database_size_mb'] = round(stats['database_size_bytes'] / (1024 * 1024), 2)

        return stats

# إنشاء مثيل عام للاستخدام
db_manager = DatabaseManager()
