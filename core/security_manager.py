#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأمان والتشفير
Security and Encryption Manager
"""

import os
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import json
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import threading

class SecurityManager:
    """مدير الأمان والتشفير للإطار"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SecurityManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الأمان"""
        if hasattr(self, 'initialized'):
            return
            
        self.initialized = True
        self.logger = logging.getLogger(__name__)
        
        # مسارات المفاتيح
        self.keys_dir = Path("data/keys")
        self.keys_dir.mkdir(parents=True, exist_ok=True)
        
        # مفاتيح التشفير
        self.master_key = None
        self.session_keys = {}
        self.api_keys = {}
        
        # إعدادات الأمان
        self.encryption_enabled = True
        self.key_rotation_interval = 86400  # 24 hours
        self.max_failed_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        
        # تتبع محاولات الدخول
        self.failed_attempts = {}
        self.locked_accounts = {}
        
        # تهيئة التشفير
        self._initialize_encryption()
        
        self.logger.info("Security Manager initialized")
    
    def _initialize_encryption(self):
        """تهيئة نظام التشفير"""
        master_key_path = self.keys_dir / "master.key"
        
        if master_key_path.exists():
            # تحميل المفتاح الموجود
            with open(master_key_path, 'rb') as f:
                self.master_key = f.read()
            self.logger.info("Master key loaded from file")
        else:
            # إنشاء مفتاح جديد
            self.master_key = Fernet.generate_key()
            with open(master_key_path, 'wb') as f:
                f.write(self.master_key)
            # تعيين صلاحيات آمنة للملف
            os.chmod(master_key_path, 0o600)
            self.logger.info("New master key generated and saved")
    
    def encrypt_data(self, data: str, key: bytes = None) -> str:
        """
        تشفير البيانات
        
        Args:
            data: البيانات المراد تشفيرها
            key: مفتاح التشفير (اختياري)
            
        Returns:
            str: البيانات المشفرة (base64)
        """
        try:
            if not self.encryption_enabled:
                return data
            
            encryption_key = key or self.master_key
            fernet = Fernet(encryption_key)
            
            encrypted_data = fernet.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str, key: bytes = None) -> str:
        """
        فك تشفير البيانات
        
        Args:
            encrypted_data: البيانات المشفرة (base64)
            key: مفتاح فك التشفير (اختياري)
            
        Returns:
            str: البيانات المفكوكة التشفير
        """
        try:
            if not self.encryption_enabled:
                return encrypted_data
            
            decryption_key = key or self.master_key
            fernet = Fernet(decryption_key)
            
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise
    
    def generate_session_key(self, session_id: str) -> bytes:
        """
        توليد مفتاح جلسة
        
        Args:
            session_id: معرف الجلسة
            
        Returns:
            bytes: مفتاح الجلسة
        """
        session_key = Fernet.generate_key()
        self.session_keys[session_id] = {
            'key': session_key,
            'created_at': datetime.now(),
            'last_used': datetime.now()
        }
        
        self.logger.info(f"Session key generated for session: {session_id}")
        return session_key
    
    def get_session_key(self, session_id: str) -> Optional[bytes]:
        """
        الحصول على مفتاح الجلسة
        
        Args:
            session_id: معرف الجلسة
            
        Returns:
            bytes: مفتاح الجلسة أو None
        """
        if session_id in self.session_keys:
            session_data = self.session_keys[session_id]
            session_data['last_used'] = datetime.now()
            return session_data['key']
        return None
    
    def revoke_session_key(self, session_id: str):
        """
        إلغاء مفتاح الجلسة
        
        Args:
            session_id: معرف الجلسة
        """
        if session_id in self.session_keys:
            del self.session_keys[session_id]
            self.logger.info(f"Session key revoked for session: {session_id}")
    
    def hash_password(self, password: str, salt: bytes = None) -> Tuple[str, str]:
        """
        تشفير كلمة المرور
        
        Args:
            password: كلمة المرور
            salt: الملح (اختياري)
            
        Returns:
            Tuple[str, str]: (كلمة المرور المشفرة, الملح)
        """
        if salt is None:
            salt = secrets.token_bytes(32)
        
        # استخدام PBKDF2 مع SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        hashed_password = kdf.derive(password.encode('utf-8'))
        
        return (
            base64.b64encode(hashed_password).decode('utf-8'),
            base64.b64encode(salt).decode('utf-8')
        )
    
    def verify_password(self, password: str, hashed_password: str, salt: str) -> bool:
        """
        التحقق من كلمة المرور
        
        Args:
            password: كلمة المرور المدخلة
            hashed_password: كلمة المرور المشفرة
            salt: الملح
            
        Returns:
            bool: True إذا كانت كلمة المرور صحيحة
        """
        try:
            salt_bytes = base64.b64decode(salt.encode('utf-8'))
            expected_hash = base64.b64decode(hashed_password.encode('utf-8'))
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt_bytes,
                iterations=100000,
            )
            
            kdf.verify(password.encode('utf-8'), expected_hash)
            return True
            
        except Exception:
            return False
    
    def generate_api_key(self, user_id: str, permissions: List[str] = None) -> str:
        """
        توليد مفتاح API
        
        Args:
            user_id: معرف المستخدم
            permissions: قائمة الصلاحيات
            
        Returns:
            str: مفتاح API
        """
        api_key = secrets.token_urlsafe(32)
        
        self.api_keys[api_key] = {
            'user_id': user_id,
            'permissions': permissions or [],
            'created_at': datetime.now(),
            'last_used': None,
            'active': True
        }
        
        self.logger.info(f"API key generated for user: {user_id}")
        return api_key
    
    def validate_api_key(self, api_key: str, required_permission: str = None) -> Optional[Dict]:
        """
        التحقق من صحة مفتاح API
        
        Args:
            api_key: مفتاح API
            required_permission: الصلاحية المطلوبة
            
        Returns:
            Dict: معلومات المفتاح أو None
        """
        if api_key not in self.api_keys:
            return None
        
        key_data = self.api_keys[api_key]
        
        if not key_data['active']:
            return None
        
        if required_permission and required_permission not in key_data['permissions']:
            return None
        
        # تحديث آخر استخدام
        key_data['last_used'] = datetime.now()
        
        return key_data
    
    def revoke_api_key(self, api_key: str):
        """
        إلغاء مفتاح API
        
        Args:
            api_key: مفتاح API
        """
        if api_key in self.api_keys:
            self.api_keys[api_key]['active'] = False
            self.logger.info(f"API key revoked: {api_key[:8]}...")
    
    def check_rate_limit(self, identifier: str, max_requests: int = 100, 
                        window_minutes: int = 60) -> bool:
        """
        فحص حد المعدل
        
        Args:
            identifier: معرف المستخدم أو IP
            max_requests: الحد الأقصى للطلبات
            window_minutes: نافزة الوقت بالدقائق
            
        Returns:
            bool: True إذا كان ضمن الحد المسموح
        """
        # تنفيذ بسيط لحد المعدل
        # يمكن تحسينه باستخدام Redis أو قاعدة بيانات
        current_time = datetime.now()
        
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        # إزالة الطلبات القديمة
        cutoff_time = current_time - timedelta(minutes=window_minutes)
        self.failed_attempts[identifier] = [
            timestamp for timestamp in self.failed_attempts[identifier]
            if timestamp > cutoff_time
        ]
        
        # فحص الحد
        if len(self.failed_attempts[identifier]) >= max_requests:
            return False
        
        # إضافة الطلب الحالي
        self.failed_attempts[identifier].append(current_time)
        return True
    
    def record_failed_attempt(self, identifier: str):
        """
        تسجيل محاولة فاشلة
        
        Args:
            identifier: معرف المستخدم أو IP
        """
        current_time = datetime.now()
        
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        self.failed_attempts[identifier].append(current_time)
        
        # فحص إذا تجاوز الحد الأقصى
        recent_attempts = [
            timestamp for timestamp in self.failed_attempts[identifier]
            if timestamp > current_time - timedelta(minutes=15)
        ]
        
        if len(recent_attempts) >= self.max_failed_attempts:
            self.locked_accounts[identifier] = current_time + timedelta(seconds=self.lockout_duration)
            self.logger.warning(f"Account locked due to failed attempts: {identifier}")
    
    def is_account_locked(self, identifier: str) -> bool:
        """
        فحص إذا كان الحساب مقفل
        
        Args:
            identifier: معرف المستخدم أو IP
            
        Returns:
            bool: True إذا كان الحساب مقفل
        """
        if identifier in self.locked_accounts:
            if datetime.now() < self.locked_accounts[identifier]:
                return True
            else:
                # انتهت فترة القفل
                del self.locked_accounts[identifier]
        
        return False
    
    def clear_failed_attempts(self, identifier: str):
        """
        مسح محاولات الفشل
        
        Args:
            identifier: معرف المستخدم أو IP
        """
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]
        
        if identifier in self.locked_accounts:
            del self.locked_accounts[identifier]
    
    def generate_secure_token(self, length: int = 32) -> str:
        """
        توليد رمز آمن
        
        Args:
            length: طول الرمز
            
        Returns:
            str: الرمز الآمن
        """
        return secrets.token_urlsafe(length)
    
    def get_security_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الأمان
        
        Returns:
            Dict: إحصائيات الأمان
        """
        current_time = datetime.now()
        
        # تنظيف البيانات القديمة
        cutoff_time = current_time - timedelta(hours=24)
        
        active_sessions = len([
            session for session in self.session_keys.values()
            if session['last_used'] > cutoff_time
        ])
        
        active_api_keys = len([
            key for key in self.api_keys.values()
            if key['active'] and (key['last_used'] is None or key['last_used'] > cutoff_time)
        ])
        
        locked_accounts_count = len([
            lockout_time for lockout_time in self.locked_accounts.values()
            if lockout_time > current_time
        ])
        
        return {
            'encryption_enabled': self.encryption_enabled,
            'active_sessions': active_sessions,
            'total_session_keys': len(self.session_keys),
            'active_api_keys': active_api_keys,
            'total_api_keys': len(self.api_keys),
            'locked_accounts': locked_accounts_count,
            'failed_attempts_tracked': len(self.failed_attempts),
            'master_key_exists': self.master_key is not None
        }

# إنشاء مثيل عام للاستخدام
security_manager = SecurityManager()
