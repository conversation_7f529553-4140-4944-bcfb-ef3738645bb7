#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد البيانات الحقيقية
Real Data Generator for Security Framework
"""

import requests
import json
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import time
from pathlib import Path

class RealDataGenerator:
    """مولد بيانات حقيقية للاختبار والتدريب"""

    def __init__(self, config: Dict[str, Any] = None):
        """
        تهيئة مولد البيانات الحقيقية

        Args:
            config: إعدادات المولد
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # مصادر البيانات الحقيقية
        self.data_sources = {
            'cve': 'https://services.nvd.nist.gov/rest/json/cves/1.0',
            'shodan': 'https://api.shodan.io/shodan/host/',
            'virustotal': 'https://www.virustotal.com/vtapi/v2/',
            'mitre_attack': 'https://attack.mitre.org/api/v2/',
            'threat_feeds': [
                'https://otx.alienvault.com/api/v1/indicators/export',
                'https://api.abuse.ch/api/v2/',
                'https://urlhaus-api.abuse.ch/v1/'
            ]
        }

        # مفاتيح API
        self.api_keys = self.config.get('api_keys', {})

        # إعدادات التخزين المؤقت
        self.cache_dir = Path("data/cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info("Real Data Generator initialized")

    def fetch_real_vulnerabilities(self, days_back: int = 30, max_results: int = 100) -> List[Dict]:
        """
        جلب ثغرات حقيقية من NVD

        Args:
            days_back: عدد الأيام للبحث في الماضي
            max_results: الحد الأقصى للنتائج

        Returns:
            List[Dict]: قائمة الثغرات الحقيقية
        """
        try:
            self.logger.info(f"Fetching real vulnerabilities from last {days_back} days")

            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            params = {
                'pubStartDate': start_date.strftime('%Y-%m-%dT%H:%M:%S:000 UTC-00:00'),
                'pubEndDate': end_date.strftime('%Y-%m-%dT%H:%M:%S:000 UTC-00:00'),
                'resultsPerPage': min(max_results, 2000)  # NVD API limit
            }

            response = requests.get(self.data_sources['cve'], params=params, timeout=30)
            response.raise_for_status()

            cve_data = response.json()
            vulnerabilities = []

            for item in cve_data.get('result', {}).get('CVE_Items', []):
                cve = item.get('cve', {})
                impact = item.get('impact', {})

                vuln = {
                    'cve_id': cve.get('CVE_data_meta', {}).get('ID', ''),
                    'description': self._extract_description(cve),
                    'severity': self._calculate_severity(impact),
                    'cvss_score': self._extract_cvss_score(impact),
                    'published_date': item.get('publishedDate', ''),
                    'modified_date': item.get('lastModifiedDate', ''),
                    'affected_products': self._extract_affected_products(cve),
                    'reference_urls': self._extract_references(cve),
                    'exploit_available': self._check_exploit_availability(cve.get('CVE_data_meta', {}).get('ID', ''))
                }

                vulnerabilities.append(vuln)

            self.logger.info(f"Fetched {len(vulnerabilities)} real vulnerabilities")

            # حفظ في التخزين المؤقت
            self._cache_data('vulnerabilities', vulnerabilities)

            return vulnerabilities

        except Exception as e:
            self.logger.error(f"Error fetching real vulnerabilities: {e}")
            return self._load_cached_data('vulnerabilities', [])

    def _extract_description(self, cve_data: Dict) -> str:
        """استخراج وصف الثغرة"""
        descriptions = cve_data.get('description', {}).get('description_data', [])
        if descriptions:
            return descriptions[0].get('value', '')
        return ''

    def _calculate_severity(self, impact_data: Dict) -> float:
        """حساب درجة الخطورة"""
        if 'baseMetricV3' in impact_data:
            return impact_data['baseMetricV3'].get('cvssV3', {}).get('baseScore', 0.0)
        elif 'baseMetricV2' in impact_data:
            return impact_data['baseMetricV2'].get('cvssV2', {}).get('baseScore', 0.0)
        return 0.0

    def _extract_cvss_score(self, impact_data: Dict) -> float:
        """استخراج نقاط CVSS"""
        return self._calculate_severity(impact_data)

    def _extract_affected_products(self, cve_data: Dict) -> List[str]:
        """استخراج المنتجات المتأثرة"""
        products = []
        affects = cve_data.get('affects', {}).get('vendor', {}).get('vendor_data', [])

        for vendor in affects:
            vendor_name = vendor.get('vendor_name', '')
            for product in vendor.get('product', {}).get('product_data', []):
                product_name = product.get('product_name', '')
                if vendor_name and product_name:
                    products.append(f"{vendor_name} {product_name}")

        return products

    def _extract_references(self, cve_data: Dict) -> List[str]:
        """استخراج المراجع"""
        references = []
        refs = cve_data.get('references', {}).get('reference_data', [])

        for ref in refs:
            url = ref.get('url', '')
            if url:
                references.append(url)

        return references

    def _check_exploit_availability(self, cve_id: str) -> bool:
        """فحص توفر استغلال للثغرة"""
        # يمكن تحسين هذا بالبحث في قواعد بيانات الاستغلالات
        # مثل Exploit-DB أو Metasploit
        return random.choice([True, False])  # مؤقت للتجربة

    def generate_realistic_network_traffic(self, duration_hours: int = 24) -> List[Dict]:
        """
        توليد حركة مرور شبكة واقعية

        Args:
            duration_hours: مدة التوليد بالساعات

        Returns:
            List[Dict]: بيانات حركة المرور
        """
        self.logger.info(f"Generating realistic network traffic for {duration_hours} hours")

        traffic_data = []
        start_time = datetime.now() - timedelta(hours=duration_hours)

        # أنماط حركة المرور الطبيعية (ساعات العمل)
        business_hours_pattern = [0.1, 0.2, 0.3, 0.5, 0.8, 1.0, 0.9, 0.7, 0.4, 0.2]

        for hour in range(duration_hours):
            current_time = start_time + timedelta(hours=hour)
            hour_of_day = current_time.hour

            # تحديد مستوى النشاط بناءً على الوقت
            if 8 <= hour_of_day <= 17:  # ساعات العمل
                base_activity = business_hours_pattern[min(hour_of_day - 8, len(business_hours_pattern) - 1)]
            else:
                base_activity = random.uniform(0.05, 0.2)

            # إضافة تباين عشوائي
            activity_level = base_activity * random.uniform(0.8, 1.2)

            # توليد أحداث أمنية عشوائية
            security_events = self._generate_security_events(activity_level)

            traffic_entry = {
                'timestamp': current_time.isoformat(),
                'total_packets': int(activity_level * 10000),
                'total_bytes': int(activity_level * 50000000),
                'unique_ips': int(activity_level * 500),
                'protocols': {
                    'http': int(activity_level * 4000),
                    'https': int(activity_level * 3000),
                    'dns': int(activity_level * 2000),
                    'smtp': int(activity_level * 500),
                    'ssh': int(activity_level * 100),
                    'ftp': int(activity_level * 50),
                    'other': int(activity_level * 350)
                },
                'suspicious_activity': activity_level > 0.8,
                'security_events': security_events,
                'top_source_ips': self._generate_top_ips(int(activity_level * 10)),
                'top_destination_ports': self._generate_top_ports(),
                'malware_detections': int(activity_level * random.uniform(0, 5)),
                'failed_login_attempts': int(activity_level * random.uniform(0, 20))
            }

            traffic_data.append(traffic_entry)

        self.logger.info(f"Generated {len(traffic_data)} traffic entries")

        # حفظ في التخزين المؤقت
        self._cache_data('network_traffic', traffic_data)

        return traffic_data

    def _generate_security_events(self, activity_level: float) -> List[Dict]:
        """توليد أحداث أمنية"""
        events = []

        # احتمالية حدوث أحداث أمنية
        if random.random() < activity_level * 0.1:
            event_types = [
                'port_scan', 'brute_force', 'malware_detection',
                'suspicious_dns', 'data_exfiltration', 'privilege_escalation'
            ]

            event = {
                'type': random.choice(event_types),
                'severity': random.choice(['low', 'medium', 'high', 'critical']),
                'source_ip': self._generate_random_ip(),
                'target_ip': self._generate_random_ip(),
                'timestamp': datetime.now().isoformat(),
                'details': f"Detected {random.choice(event_types)} activity"
            }
            events.append(event)

        return events

    def _generate_top_ips(self, count: int) -> List[Dict]:
        """توليد قائمة أهم عناوين IP"""
        ips = []
        for _ in range(min(count, 20)):
            ips.append({
                'ip': self._generate_random_ip(),
                'requests': random.randint(100, 10000),
                'bytes': random.randint(1000000, 100000000)
            })
        return sorted(ips, key=lambda x: x['requests'], reverse=True)

    def _generate_top_ports(self) -> List[Dict]:
        """توليد قائمة أهم المنافذ"""
        common_ports = [80, 443, 22, 21, 25, 53, 110, 143, 993, 995]
        ports = []

        for port in common_ports:
            ports.append({
                'port': port,
                'connections': random.randint(50, 5000),
                'protocol': 'TCP' if port != 53 else 'UDP'
            })

        return sorted(ports, key=lambda x: x['connections'], reverse=True)

    def _generate_random_ip(self) -> str:
        """توليد عنوان IP عشوائي"""
        return f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"

    def _cache_data(self, data_type: str, data: Any):
        """حفظ البيانات في التخزين المؤقت"""
        try:
            cache_file = self.cache_dir / f"{data_type}_{datetime.now().strftime('%Y%m%d')}.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.debug(f"Data cached to {cache_file}")
        except Exception as e:
            self.logger.error(f"Error caching data: {e}")

    def _load_cached_data(self, data_type: str, default: Any = None) -> Any:
        """تحميل البيانات من التخزين المؤقت"""
        try:
            cache_file = self.cache_dir / f"{data_type}_{datetime.now().strftime('%Y%m%d')}.json"
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.logger.debug(f"Data loaded from cache: {cache_file}")
                return data
        except Exception as e:
            self.logger.error(f"Error loading cached data: {e}")

        return default if default is not None else []

    def create_realistic_user_behavior(self, num_users: int = 100, days: int = 7) -> List[Dict]:
        """
        إنشاء سلوك مستخدمين واقعي

        Args:
            num_users: عدد المستخدمين
            days: عدد الأيام

        Returns:
            List[Dict]: بيانات سلوك المستخدمين
        """
        self.logger.info(f"Creating realistic user behavior for {num_users} users over {days} days")

        users_data = []
        departments = ['IT', 'HR', 'Finance', 'Marketing', 'Operations', 'Sales', 'Legal']
        roles = ['employee', 'manager', 'admin', 'contractor', 'intern']
        applications = ['email', 'browser', 'office', 'crm', 'erp', 'chat', 'vpn']

        for user_id in range(num_users):
            user_profile = {
                'user_id': f"user_{user_id:03d}",
                'username': f"user{user_id}@company.com",
                'department': random.choice(departments),
                'role': random.choice(roles),
                'login_patterns': [],
                'application_usage': {},
                'file_access_patterns': [],
                'email_activity': [],
                'security_incidents': []
            }

            # أنماط تسجيل الدخول
            for day in range(days):
                if random.random() > 0.1:  # 90% احتمال الحضور
                    login_time = datetime.now() - timedelta(days=days-day) + timedelta(
                        hours=random.uniform(7, 9),
                        minutes=random.uniform(0, 59)
                    )
                    logout_time = login_time + timedelta(hours=random.uniform(7, 10))

                    user_profile['login_patterns'].append({
                        'date': login_time.date().isoformat(),
                        'login_time': login_time.time().isoformat(),
                        'logout_time': logout_time.time().isoformat(),
                        'duration_hours': (logout_time - login_time).total_seconds() / 3600,
                        'login_method': random.choice(['password', 'sso', '2fa']),
                        'device_type': random.choice(['desktop', 'laptop', 'mobile']),
                        'ip_address': self._generate_random_ip(),
                        'location': random.choice(['office', 'home', 'remote'])
                    })

            # استخدام التطبيقات
            for app in applications:
                user_profile['application_usage'][app] = {
                    'daily_hours': round(random.uniform(0.5, 6), 2),
                    'frequency': random.choice(['high', 'medium', 'low']),
                    'last_used': (datetime.now() - timedelta(days=random.randint(0, 7))).isoformat(),
                    'total_sessions': random.randint(10, 200),
                    'average_session_duration': round(random.uniform(10, 120), 2)  # minutes
                }

            # أنماط الوصول للملفات
            file_types = ['documents', 'spreadsheets', 'presentations', 'images', 'databases']
            for file_type in file_types:
                user_profile['file_access_patterns'].append({
                    'file_type': file_type,
                    'access_count': random.randint(5, 100),
                    'last_access': (datetime.now() - timedelta(days=random.randint(0, 30))).isoformat(),
                    'permissions': random.choice(['read', 'write', 'admin']),
                    'sensitive_data': random.choice([True, False])
                })

            # نشاط البريد الإلكتروني
            user_profile['email_activity'] = {
                'emails_sent': random.randint(10, 200),
                'emails_received': random.randint(50, 500),
                'attachments_downloaded': random.randint(0, 50),
                'suspicious_emails_reported': random.randint(0, 5),
                'phishing_attempts_detected': random.randint(0, 3)
            }

            # حوادث أمنية (إن وجدت)
            if random.random() < 0.1:  # 10% احتمال وجود حادث أمني
                incident_types = ['malware_detection', 'suspicious_login', 'data_breach', 'policy_violation']
                user_profile['security_incidents'].append({
                    'type': random.choice(incident_types),
                    'date': (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                    'severity': random.choice(['low', 'medium', 'high']),
                    'resolved': random.choice([True, False]),
                    'description': f"Security incident detected for user {user_profile['user_id']}"
                })

            users_data.append(user_profile)

        self.logger.info(f"Generated behavior data for {len(users_data)} users")

        # حفظ في التخزين المؤقت
        self._cache_data('user_behavior', users_data)

        return users_data

    def simulate_attack_scenarios(self) -> List[Dict]:
        """
        محاكاة سيناريوهات هجوم حقيقية

        Returns:
            List[Dict]: سيناريوهات الهجوم
        """
        self.logger.info("Simulating realistic attack scenarios")

        attack_scenarios = [
            {
                'name': 'Phishing Campaign',
                'type': 'social_engineering',
                'duration_days': random.randint(1, 7),
                'targets': random.randint(10, 100),
                'success_rate': round(random.uniform(0.05, 0.15), 3),
                'indicators': [
                    'suspicious_email_domains',
                    'credential_harvesting_attempts',
                    'unusual_login_locations'
                ],
                'timeline': self._generate_attack_timeline('phishing'),
                'impact': {
                    'compromised_accounts': random.randint(1, 15),
                    'data_accessed': random.choice(['email', 'documents', 'credentials']),
                    'financial_loss': random.randint(1000, 50000)
                }
            },
            {
                'name': 'Ransomware Attack',
                'type': 'malware',
                'duration_hours': random.randint(2, 48),
                'affected_systems': random.randint(5, 50),
                'encryption_percentage': round(random.uniform(0.3, 0.9), 2),
                'indicators': [
                    'file_encryption_activity',
                    'ransom_note_creation',
                    'network_lateral_movement'
                ],
                'timeline': self._generate_attack_timeline('ransomware'),
                'impact': {
                    'encrypted_files': random.randint(1000, 100000),
                    'downtime_hours': random.randint(24, 168),
                    'ransom_demand': random.randint(10000, 1000000)
                }
            },
            {
                'name': 'APT Campaign',
                'type': 'advanced_persistent_threat',
                'duration_months': random.randint(1, 12),
                'persistence_methods': ['registry_modification', 'scheduled_tasks', 'service_installation'],
                'data_exfiltration_gb': round(random.uniform(1, 100), 2),
                'indicators': [
                    'command_and_control_communication',
                    'privilege_escalation_attempts',
                    'data_staging_activities'
                ],
                'timeline': self._generate_attack_timeline('apt'),
                'impact': {
                    'data_stolen': random.choice(['customer_data', 'financial_records', 'intellectual_property']),
                    'systems_compromised': random.randint(10, 100),
                    'detection_time_days': random.randint(30, 365)
                }
            }
        ]

        # إضافة تفاصيل إضافية لكل سيناريو
        for scenario in attack_scenarios:
            scenario.update({
                'id': str(random.randint(100000, 999999)),
                'created_at': datetime.now().isoformat(),
                'threat_actor': random.choice(['cybercriminal_group', 'nation_state', 'insider_threat', 'hacktivist']),
                'attack_vector': random.choice(['email', 'web', 'network', 'physical', 'supply_chain']),
                'mitre_techniques': self._get_random_mitre_techniques(),
                'detection_methods': random.sample([
                    'antivirus', 'ids_ips', 'siem', 'edr', 'user_report', 'threat_hunting'
                ], random.randint(1, 3))
            })

        self.logger.info(f"Generated {len(attack_scenarios)} attack scenarios")

        # حفظ في التخزين المؤقت
        self._cache_data('attack_scenarios', attack_scenarios)

        return attack_scenarios

    def _generate_attack_timeline(self, attack_type: str) -> List[Dict]:
        """توليد جدول زمني للهجوم"""
        if attack_type == 'phishing':
            return [
                {'phase': 'reconnaissance', 'duration_hours': 24, 'activities': ['target_research', 'email_harvesting']},
                {'phase': 'initial_access', 'duration_hours': 2, 'activities': ['phishing_email_sent', 'credential_harvesting']},
                {'phase': 'execution', 'duration_hours': 1, 'activities': ['malicious_payload_execution']},
                {'phase': 'persistence', 'duration_hours': 0.5, 'activities': ['backdoor_installation']}
            ]
        elif attack_type == 'ransomware':
            return [
                {'phase': 'initial_access', 'duration_hours': 1, 'activities': ['exploit_vulnerability', 'credential_stuffing']},
                {'phase': 'execution', 'duration_hours': 0.5, 'activities': ['malware_deployment']},
                {'phase': 'lateral_movement', 'duration_hours': 4, 'activities': ['network_scanning', 'credential_dumping']},
                {'phase': 'impact', 'duration_hours': 2, 'activities': ['file_encryption', 'ransom_demand']}
            ]
        elif attack_type == 'apt':
            return [
                {'phase': 'reconnaissance', 'duration_days': 30, 'activities': ['osint_gathering', 'infrastructure_mapping']},
                {'phase': 'initial_access', 'duration_days': 7, 'activities': ['spear_phishing', 'watering_hole']},
                {'phase': 'persistence', 'duration_days': 2, 'activities': ['backdoor_installation', 'registry_modification']},
                {'phase': 'privilege_escalation', 'duration_days': 5, 'activities': ['exploit_local_vulnerabilities']},
                {'phase': 'lateral_movement', 'duration_days': 14, 'activities': ['credential_harvesting', 'network_traversal']},
                {'phase': 'collection', 'duration_days': 30, 'activities': ['data_identification', 'data_staging']},
                {'phase': 'exfiltration', 'duration_days': 7, 'activities': ['data_compression', 'covert_channels']}
            ]
        return []

    def _get_random_mitre_techniques(self) -> List[str]:
        """الحصول على تقنيات MITRE ATT&CK عشوائية"""
        techniques = [
            'T1566.001', 'T1059.001', 'T1055', 'T1003.001', 'T1021.001',
            'T1083', 'T1082', 'T1016', 'T1033', 'T1057', 'T1012', 'T1005',
            'T1041', 'T1486', 'T1490', 'T1489', 'T1562.001'
        ]
        return random.sample(techniques, random.randint(3, 8))

    def get_data_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات البيانات المولدة

        Returns:
            Dict: إحصائيات البيانات
        """
        stats = {
            'cache_directory': str(self.cache_dir),
            'cached_files': [],
            'total_cache_size_mb': 0
        }

        if self.cache_dir.exists():
            for cache_file in self.cache_dir.glob('*.json'):
                file_size = cache_file.stat().st_size
                stats['cached_files'].append({
                    'name': cache_file.name,
                    'size_bytes': file_size,
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(cache_file.stat().st_mtime).isoformat()
                })
                stats['total_cache_size_mb'] += file_size / (1024 * 1024)

        stats['total_cache_size_mb'] = round(stats['total_cache_size_mb'], 2)
        stats['total_cached_files'] = len(stats['cached_files'])

        return stats
