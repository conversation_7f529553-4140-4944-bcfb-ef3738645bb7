#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الإعدادات المركزي
Central Configuration Manager
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
import threading

class ConfigManager:
    """مدير الإعدادات المركزي للإطار"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير الإعدادات"""
        if hasattr(self, 'initialized'):
            return
            
        self.initialized = True
        self.logger = logging.getLogger(__name__)
        self.config_data = {}
        self.config_file_path = None
        self.watchers = []  # مراقبي تغيير الإعدادات
        
        # تحميل الإعدادات الافتراضية
        self._load_default_config()
    
    def _load_default_config(self):
        """تحميل الإعدادات الافتراضية"""
        self.config_data = {
            "general": {
                "framework_name": "Security Exploitation Framework",
                "version": "1.0.0",
                "debug_mode": False,
                "log_level": "INFO",
                "max_concurrent_sessions": 10,
                "session_timeout": 3600
            },
            "database": {
                "type": "sqlite",
                "sqlite": {
                    "path": "data/databases/framework.db"
                }
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_path": "logs/framework.log",
                "max_file_size": "10MB",
                "backup_count": 5,
                "console_output": True
            },
            "security": {
                "encryption_key_path": "data/keys/master.key",
                "session_encryption": True,
                "api_authentication": True,
                "rate_limiting": True,
                "max_requests_per_minute": 100
            },
            "real_data": {
                "enabled": True,
                "sources": {
                    "cve_database": {
                        "url": "https://services.nvd.nist.gov/rest/json/cves/1.0",
                        "update_interval": 86400
                    }
                }
            }
        }
    
    def load_config(self, config_path: str) -> bool:
        """
        تحميل ملف الإعدادات
        
        Args:
            config_path: مسار ملف الإعدادات
            
        Returns:
            bool: True إذا تم التحميل بنجاح
        """
        try:
            config_path = Path(config_path)
            
            if not config_path.exists():
                self.logger.error(f"Config file not found: {config_path}")
                return False
            
            self.config_file_path = config_path
            
            # تحديد نوع الملف وتحميله
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_config = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
            else:
                self.logger.error(f"Unsupported config file format: {config_path.suffix}")
                return False
            
            # دمج الإعدادات المحملة مع الافتراضية
            self._merge_config(loaded_config)
            
            self.logger.info(f"Configuration loaded from: {config_path}")
            
            # إشعار المراقبين
            self._notify_watchers("config_loaded", {"path": str(config_path)})
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return False
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """
        دمج الإعدادات الجديدة مع الموجودة
        
        Args:
            new_config: الإعدادات الجديدة
        """
        def merge_dict(base: Dict, update: Dict) -> Dict:
            """دمج القواميس بشكل عميق"""
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
            return base
        
        merge_dict(self.config_data, new_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد
        
        Args:
            key: مفتاح الإعداد (يدعم النقاط للوصول العميق مثل "database.type")
            default: القيمة الافتراضية
            
        Returns:
            Any: قيمة الإعداد
        """
        try:
            keys = key.split('.')
            value = self.config_data
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.logger.error(f"Error getting config value for key '{key}': {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        تعيين قيمة إعداد
        
        Args:
            key: مفتاح الإعداد
            value: القيمة الجديدة
            
        Returns:
            bool: True إذا تم التعيين بنجاح
        """
        try:
            keys = key.split('.')
            config = self.config_data
            
            # الوصول إلى المستوى الأخير
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # تعيين القيمة
            old_value = config.get(keys[-1])
            config[keys[-1]] = value
            
            self.logger.info(f"Config updated: {key} = {value}")
            
            # إشعار المراقبين
            self._notify_watchers("config_changed", {
                "key": key,
                "old_value": old_value,
                "new_value": value
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting config value for key '{key}': {e}")
            return False
    
    def save_config(self, output_path: str = None) -> bool:
        """
        حفظ الإعدادات إلى ملف
        
        Args:
            output_path: مسار الحفظ (اختياري)
            
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if output_path is None:
                if self.config_file_path is None:
                    self.logger.error("No config file path specified")
                    return False
                output_path = self.config_file_path
            
            output_path = Path(output_path)
            
            # إنشاء المجلد إذا لم يكن موجوداً
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # حفظ حسب نوع الملف
            if output_path.suffix.lower() in ['.yaml', '.yml']:
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self.config_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            elif output_path.suffix.lower() == '.json':
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            else:
                self.logger.error(f"Unsupported output format: {output_path.suffix}")
                return False
            
            self.logger.info(f"Configuration saved to: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        الحصول على قسم كامل من الإعدادات
        
        Args:
            section: اسم القسم
            
        Returns:
            Dict: إعدادات القسم
        """
        return self.get(section, {})
    
    def validate_config(self) -> Dict[str, Any]:
        """
        التحقق من صحة الإعدادات
        
        Returns:
            Dict: نتائج التحقق
        """
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # التحقق من الأقسام المطلوبة
            required_sections = ["general", "database", "logging", "security"]
            for section in required_sections:
                if section not in self.config_data:
                    validation_results["errors"].append(f"Missing required section: {section}")
                    validation_results["valid"] = False
            
            # التحقق من إعدادات قاعدة البيانات
            db_type = self.get("database.type")
            if db_type not in ["sqlite", "postgresql", "mongodb"]:
                validation_results["errors"].append(f"Invalid database type: {db_type}")
                validation_results["valid"] = False
            
            # التحقق من مستوى التسجيل
            log_level = self.get("logging.level")
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                validation_results["warnings"].append(f"Invalid log level: {log_level}")
            
            # التحقق من وجود مفاتيح API
            if self.get("real_data.sources.shodan.enabled") and not self.get("real_data.sources.shodan.api_key"):
                validation_results["warnings"].append("Shodan API key not configured")
            
            if self.get("real_data.sources.virustotal.enabled") and not self.get("real_data.sources.virustotal.api_key"):
                validation_results["warnings"].append("VirusTotal API key not configured")
            
        except Exception as e:
            validation_results["errors"].append(f"Validation error: {e}")
            validation_results["valid"] = False
        
        return validation_results
    
    def add_watcher(self, callback):
        """
        إضافة مراقب لتغييرات الإعدادات
        
        Args:
            callback: دالة الاستدعاء عند التغيير
        """
        self.watchers.append(callback)
    
    def remove_watcher(self, callback):
        """
        إزالة مراقب الإعدادات
        
        Args:
            callback: دالة الاستدعاء المراد إزالتها
        """
        if callback in self.watchers:
            self.watchers.remove(callback)
    
    def _notify_watchers(self, event_type: str, data: Dict[str, Any]):
        """
        إشعار المراقبين بالتغييرات
        
        Args:
            event_type: نوع الحدث
            data: بيانات الحدث
        """
        for watcher in self.watchers:
            try:
                watcher(event_type, data)
            except Exception as e:
                self.logger.error(f"Error notifying config watcher: {e}")
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        الحصول على جميع الإعدادات
        
        Returns:
            Dict: جميع الإعدادات
        """
        return self.config_data.copy()
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.config_data.clear()
        self._load_default_config()
        self.logger.info("Configuration reset to defaults")
        self._notify_watchers("config_reset", {})

# إنشاء مثيل عام للاستخدام
config = ConfigManager()
