#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التسجيل الموحد
Unified Logging System
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import json
import threading
from enum import Enum

class LogLevel(Enum):
    """مستويات التسجيل"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class SecurityLogger:
    """نظام تسجيل موحد للإطار الأمني"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SecurityLogger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """تهيئة نظام التسجيل"""
        if hasattr(self, 'initialized'):
            return
            
        self.initialized = True
        self.loggers = {}
        self.handlers = {}
        self.formatters = {}
        self.config = {}
        
        # إعداد التسجيل الافتراضي
        self._setup_default_logging()
    
    def _setup_default_logging(self):
        """إعداد التسجيل الافتراضي"""
        # إنشاء مجلد السجلات
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # إعداد التنسيق الافتراضي
        self.formatters['default'] = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        self.formatters['detailed'] = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s'
        )
        
        self.formatters['security'] = logging.Formatter(
            '%(asctime)s - SECURITY - %(name)s - %(levelname)s - %(message)s'
        )
        
        # إعداد المعالجات الافتراضية
        self._setup_console_handler()
        self._setup_file_handler()
        self._setup_security_handler()
        
        # إعداد السجل الرئيسي
        self._setup_main_logger()
    
    def _setup_console_handler(self):
        """إعداد معالج وحدة التحكم"""
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(self.formatters['default'])
        self.handlers['console'] = console_handler
    
    def _setup_file_handler(self):
        """إعداد معالج الملف الدوار"""
        log_file = Path("logs/framework.log")
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(self.formatters['detailed'])
        self.handlers['file'] = file_handler
    
    def _setup_security_handler(self):
        """إعداد معالج السجلات الأمنية"""
        security_log_file = Path("logs/security.log")
        security_handler = logging.handlers.RotatingFileHandler(
            security_log_file,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        security_handler.setLevel(logging.INFO)
        security_handler.setFormatter(self.formatters['security'])
        self.handlers['security'] = security_handler
    
    def _setup_main_logger(self):
        """إعداد السجل الرئيسي"""
        main_logger = logging.getLogger('SecurityFramework')
        main_logger.setLevel(logging.DEBUG)
        
        # إضافة المعالجات
        main_logger.addHandler(self.handlers['console'])
        main_logger.addHandler(self.handlers['file'])
        
        # منع التكرار
        main_logger.propagate = False
        
        self.loggers['main'] = main_logger
    
    def get_logger(self, name: str, level: str = "INFO") -> logging.Logger:
        """
        الحصول على سجل مخصص
        
        Args:
            name: اسم السجل
            level: مستوى التسجيل
            
        Returns:
            logging.Logger: كائن السجل
        """
        if name in self.loggers:
            return self.loggers[name]
        
        # إنشاء سجل جديد
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        
        # إضافة المعالجات
        logger.addHandler(self.handlers['console'])
        logger.addHandler(self.handlers['file'])
        
        # منع التكرار
        logger.propagate = False
        
        self.loggers[name] = logger
        return logger
    
    def get_security_logger(self) -> logging.Logger:
        """
        الحصول على سجل الأمان
        
        Returns:
            logging.Logger: سجل الأمان
        """
        if 'security' not in self.loggers:
            security_logger = logging.getLogger('SecurityFramework.Security')
            security_logger.setLevel(logging.INFO)
            security_logger.addHandler(self.handlers['security'])
            security_logger.propagate = False
            self.loggers['security'] = security_logger
        
        return self.loggers['security']
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          level: str = "INFO", user_id: str = None, 
                          session_id: str = None):
        """
        تسجيل حدث أمني
        
        Args:
            event_type: نوع الحدث
            details: تفاصيل الحدث
            level: مستوى التسجيل
            user_id: معرف المستخدم
            session_id: معرف الجلسة
        """
        security_logger = self.get_security_logger()
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "session_id": session_id,
            "details": details
        }
        
        log_message = json.dumps(log_entry, ensure_ascii=False)
        
        if level.upper() == "DEBUG":
            security_logger.debug(log_message)
        elif level.upper() == "INFO":
            security_logger.info(log_message)
        elif level.upper() == "WARNING":
            security_logger.warning(log_message)
        elif level.upper() == "ERROR":
            security_logger.error(log_message)
        elif level.upper() == "CRITICAL":
            security_logger.critical(log_message)
    
    def log_module_activity(self, module_name: str, activity: str, 
                           target: str = None, result: str = None,
                           session_id: str = None):
        """
        تسجيل نشاط الوحدة
        
        Args:
            module_name: اسم الوحدة
            activity: النشاط
            target: الهدف
            result: النتيجة
            session_id: معرف الجلسة
        """
        self.log_security_event(
            event_type="module_activity",
            details={
                "module_name": module_name,
                "activity": activity,
                "target": target,
                "result": result
            },
            session_id=session_id
        )
    
    def log_authentication(self, user_id: str, success: bool, 
                          method: str = "password", ip_address: str = None):
        """
        تسجيل محاولة المصادقة
        
        Args:
            user_id: معرف المستخدم
            success: نجح أم فشل
            method: طريقة المصادقة
            ip_address: عنوان IP
        """
        self.log_security_event(
            event_type="authentication",
            details={
                "success": success,
                "method": method,
                "ip_address": ip_address
            },
            level="INFO" if success else "WARNING",
            user_id=user_id
        )
    
    def log_authorization(self, user_id: str, resource: str, 
                         action: str, granted: bool, session_id: str = None):
        """
        تسجيل محاولة التصريح
        
        Args:
            user_id: معرف المستخدم
            resource: المورد
            action: الإجراء
            granted: تم منح التصريح أم لا
            session_id: معرف الجلسة
        """
        self.log_security_event(
            event_type="authorization",
            details={
                "resource": resource,
                "action": action,
                "granted": granted
            },
            level="INFO" if granted else "WARNING",
            user_id=user_id,
            session_id=session_id
        )
    
    def log_data_access(self, user_id: str, data_type: str, 
                       operation: str, session_id: str = None):
        """
        تسجيل الوصول للبيانات
        
        Args:
            user_id: معرف المستخدم
            data_type: نوع البيانات
            operation: العملية
            session_id: معرف الجلسة
        """
        self.log_security_event(
            event_type="data_access",
            details={
                "data_type": data_type,
                "operation": operation
            },
            user_id=user_id,
            session_id=session_id
        )
    
    def log_error(self, error_type: str, error_message: str, 
                  module_name: str = None, session_id: str = None):
        """
        تسجيل خطأ
        
        Args:
            error_type: نوع الخطأ
            error_message: رسالة الخطأ
            module_name: اسم الوحدة
            session_id: معرف الجلسة
        """
        self.log_security_event(
            event_type="error",
            details={
                "error_type": error_type,
                "error_message": error_message,
                "module_name": module_name
            },
            level="ERROR",
            session_id=session_id
        )
    
    def configure_from_dict(self, config: Dict[str, Any]):
        """
        تكوين التسجيل من قاموس الإعدادات
        
        Args:
            config: إعدادات التسجيل
        """
        self.config = config
        
        # تحديث مستوى التسجيل
        if 'level' in config:
            level = getattr(logging, config['level'].upper(), logging.INFO)
            for logger in self.loggers.values():
                logger.setLevel(level)
        
        # تحديث تنسيق التسجيل
        if 'format' in config:
            new_formatter = logging.Formatter(config['format'])
            for handler in self.handlers.values():
                handler.setFormatter(new_formatter)
        
        # تحديث مسار الملف
        if 'file_path' in config:
            self._update_file_handler(config['file_path'])
    
    def _update_file_handler(self, file_path: str):
        """
        تحديث معالج الملف
        
        Args:
            file_path: مسار الملف الجديد
        """
        # إزالة المعالج القديم
        if 'file' in self.handlers:
            for logger in self.loggers.values():
                logger.removeHandler(self.handlers['file'])
            self.handlers['file'].close()
        
        # إنشاء معالج جديد
        log_file = Path(file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(self.formatters['detailed'])
        
        self.handlers['file'] = file_handler
        
        # إضافة المعالج الجديد للسجلات
        for logger in self.loggers.values():
            logger.addHandler(file_handler)
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات السجلات
        
        Returns:
            Dict: إحصائيات السجلات
        """
        stats = {
            "active_loggers": len(self.loggers),
            "active_handlers": len(self.handlers),
            "log_files": []
        }
        
        # جمع معلومات ملفات السجلات
        logs_dir = Path("logs")
        if logs_dir.exists():
            for log_file in logs_dir.glob("*.log"):
                stats["log_files"].append({
                    "name": log_file.name,
                    "size": log_file.stat().st_size,
                    "modified": datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
        
        return stats

# إنشاء مثيل عام للاستخدام
security_logger = SecurityLogger()
