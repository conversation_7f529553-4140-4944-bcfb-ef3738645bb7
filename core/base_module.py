#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الفئة الأساسية لجميع وحدات الأمان
Base Security Module for all security modules
"""

import logging
import json
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

class ModuleStatus(Enum):
    """حالات الوحدة"""
    INITIALIZED = "initialized"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"

class ModuleType(Enum):
    """أنواع الوحدات"""
    RECONNAISSANCE = "reconnaissance"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    PERSISTENCE = "persistence"
    EVASION = "evasion"
    INTELLIGENCE = "intelligence"
    REPORTING = "reporting"

class BaseSecurityModule(ABC):
    """الفئة الأساسية لجميع وحدات الأمان"""

    def __init__(self, name: str, module_type: ModuleType, config: Dict[str, Any] = None):
        """
        تهيئة الوحدة الأساسية
        
        Args:
            name: اسم الوحدة
            module_type: نوع الوحدة
            config: إعدادات الوحدة
        """
        self.name = name
        self.module_type = module_type
        self.config = config or {}
        self.module_id = str(uuid.uuid4())
        self.logger = logging.getLogger(f"SecurityModule.{name}")
        
        # معلومات الجلسة
        self.session_id = None
        self.campaign_id = None
        
        # حالة الوحدة
        self.status = ModuleStatus.INITIALIZED
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        
        # النتائج والبيانات
        self.results = {}
        self.errors = []
        self.warnings = []
        self.progress = 0.0
        
        # إعدادات الأمان
        self.requires_authorization = True
        self.authorized = False
        self.target_authorized = False
        
        self.logger.info(f"Module {self.name} initialized with ID: {self.module_id}")

    @abstractmethod
    def execute(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        تنفيذ الوحدة الأساسية - يجب تنفيذها في الفئات المشتقة
        
        Args:
            target: الهدف المراد فحصه أو استغلاله
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج التنفيذ
        """
        pass

    @abstractmethod
    def validate_target(self, target: str) -> bool:
        """
        التحقق من صحة الهدف
        
        Args:
            target: الهدف المراد التحقق منه
            
        Returns:
            bool: True إذا كان الهدف صحيح
        """
        pass

    def pre_execution_checks(self, target: str) -> bool:
        """
        فحوصات ما قبل التنفيذ
        
        Args:
            target: الهدف
            
        Returns:
            bool: True إذا نجحت جميع الفحوصات
        """
        try:
            # التحقق من التصريح
            if self.requires_authorization and not self.authorized:
                self.logger.error("Module execution requires authorization")
                return False
            
            # التحقق من صحة الهدف
            if not self.validate_target(target):
                self.logger.error(f"Invalid target: {target}")
                return False
            
            # التحقق من التصريح للهدف
            if not self.target_authorized:
                self.logger.warning(f"Target {target} not explicitly authorized")
                # يمكن إضافة فحوصات إضافية هنا
            
            return True
            
        except Exception as e:
            self.logger.error(f"Pre-execution check failed: {e}")
            return False

    def start_execution(self, target: str, **kwargs) -> Dict[str, Any]:
        """
        بدء تنفيذ الوحدة مع الفحوصات الأمنية
        
        Args:
            target: الهدف
            **kwargs: معاملات إضافية
            
        Returns:
            Dict: نتائج التنفيذ
        """
        try:
            self.logger.info(f"Starting execution for target: {target}")
            self.status = ModuleStatus.RUNNING
            self.started_at = datetime.now()
            self.progress = 0.0
            
            # فحوصات ما قبل التنفيذ
            if not self.pre_execution_checks(target):
                self.status = ModuleStatus.FAILED
                return {"success": False, "error": "Pre-execution checks failed"}
            
            # تنفيذ الوحدة
            self.results = self.execute(target, **kwargs)
            
            # تحديث الحالة
            if self.results.get("success", False):
                self.status = ModuleStatus.COMPLETED
                self.logger.info(f"Module execution completed successfully")
            else:
                self.status = ModuleStatus.FAILED
                self.logger.error(f"Module execution failed")
            
            self.completed_at = datetime.now()
            self.progress = 100.0
            
            return self.results
            
        except Exception as e:
            self.logger.error(f"Execution failed: {e}")
            self.status = ModuleStatus.FAILED
            self.errors.append(str(e))
            return {"success": False, "error": str(e)}

    def stop_execution(self):
        """إيقاف تنفيذ الوحدة"""
        self.logger.info("Stopping module execution")
        self.status = ModuleStatus.STOPPED
        self.completed_at = datetime.now()

    def get_status(self) -> Dict[str, Any]:
        """
        الحصول على حالة الوحدة
        
        Returns:
            Dict: معلومات حالة الوحدة
        """
        return {
            "module_id": self.module_id,
            "name": self.name,
            "type": self.module_type.value,
            "status": self.status.value,
            "progress": self.progress,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "session_id": self.session_id,
            "campaign_id": self.campaign_id,
            "errors": self.errors,
            "warnings": self.warnings
        }

    def get_results(self) -> Dict[str, Any]:
        """
        الحصول على نتائج الوحدة
        
        Returns:
            Dict: نتائج التنفيذ
        """
        return {
            "module_info": self.get_status(),
            "results": self.results
        }

    def set_authorization(self, authorized: bool, target_authorized: bool = False):
        """
        تعيين حالة التصريح
        
        Args:
            authorized: تصريح الوحدة
            target_authorized: تصريح الهدف
        """
        self.authorized = authorized
        self.target_authorized = target_authorized
        self.logger.info(f"Authorization set: module={authorized}, target={target_authorized}")

    def set_session(self, session_id: str, campaign_id: str = None):
        """
        تعيين معرف الجلسة والحملة
        
        Args:
            session_id: معرف الجلسة
            campaign_id: معرف الحملة
        """
        self.session_id = session_id
        self.campaign_id = campaign_id
        self.logger.info(f"Session set: {session_id}, Campaign: {campaign_id}")

    def log_activity(self, activity: str, level: str = "INFO", data: Dict = None):
        """
        تسجيل نشاط الوحدة
        
        Args:
            activity: وصف النشاط
            level: مستوى التسجيل
            data: بيانات إضافية
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "module_id": self.module_id,
            "module_name": self.name,
            "session_id": self.session_id,
            "activity": activity,
            "data": data or {}
        }
        
        if level.upper() == "ERROR":
            self.logger.error(f"{activity}: {json.dumps(data) if data else ''}")
        elif level.upper() == "WARNING":
            self.logger.warning(f"{activity}: {json.dumps(data) if data else ''}")
        else:
            self.logger.info(f"{activity}: {json.dumps(data) if data else ''}")

    def update_progress(self, progress: float, message: str = ""):
        """
        تحديث تقدم التنفيذ
        
        Args:
            progress: نسبة التقدم (0-100)
            message: رسالة التقدم
        """
        self.progress = max(0.0, min(100.0, progress))
        if message:
            self.logger.info(f"Progress: {self.progress:.1f}% - {message}")

    def add_warning(self, warning: str):
        """
        إضافة تحذير
        
        Args:
            warning: نص التحذير
        """
        self.warnings.append({
            "timestamp": datetime.now().isoformat(),
            "message": warning
        })
        self.logger.warning(warning)

    def add_error(self, error: str):
        """
        إضافة خطأ
        
        Args:
            error: نص الخطأ
        """
        self.errors.append({
            "timestamp": datetime.now().isoformat(),
            "message": error
        })
        self.logger.error(error)

    def __str__(self) -> str:
        """تمثيل نصي للوحدة"""
        return f"SecurityModule(name={self.name}, type={self.module_type.value}, status={self.status.value})"

    def __repr__(self) -> str:
        """تمثيل تقني للوحدة"""
        return f"<SecurityModule {self.name} ({self.module_id})>"
