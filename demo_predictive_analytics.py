#!/usr/bin/env python3
"""
Demo script for Predictive Analytics Module
سكربت تجريبي لوحدة التحليلات التنبؤية

This script demonstrates the predictive analytics capabilities.
هذا السكربت يوضح قدرات التحليلات التنبؤية.
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def demo_data_collection():
    """Demo data collection capabilities"""
    print("\n" + "="*50)
    print("📊 Data Collection Demo")
    print("عرض توضيحي لجمع البيانات")
    print("="*50)
    
    try:
        from predictive_analytics import RealPredictiveAnalytics
        
        # Initialize analytics system
        analytics = RealPredictiveAnalytics(output_dir="demo_analytics_output")
        
        print("\n[*] Collecting real-time system data...")
        
        # Collect various types of data
        system_metrics = analytics.collect_system_metrics()
        network_data = analytics.collect_network_data()
        user_behavior = analytics.collect_user_behavior()
        security_events = analytics.collect_security_events()
        
        if system_metrics:
            print(f"✅ System Metrics:")
            print(f"   CPU Usage: {system_metrics.get('cpu_percent', 0):.2f}%")
            print(f"   Memory Usage: {system_metrics.get('memory_percent', 0):.2f}%")
            print(f"   Process Count: {system_metrics.get('process_count', 0)}")
            print(f"   Thread Count: {system_metrics.get('thread_count', 0)}")
        
        if network_data:
            print(f"\n✅ Network Data:")
            print(f"   Active Connections: {network_data.get('connections_count', 0)}")
            print(f"   Bytes Sent: {network_data.get('bytes_sent', 0):,}")
            print(f"   Bytes Received: {network_data.get('bytes_recv', 0):,}")
            print(f"   Network Speed: {network_data.get('network_speed', 0)} Mbps")
        
        if user_behavior:
            print(f"\n✅ User Behavior:")
            print(f"   Active Window: {user_behavior.get('active_window', 'Unknown')}")
            print(f"   Activity Score: {user_behavior.get('user_activity_score', 0):.2f}")
            print(f"   Session Duration: {user_behavior.get('session_duration', 0):.0f}s")
        
        if security_events:
            print(f"\n✅ Security Events: {len(security_events)} detected")
            for event in security_events[:2]:
                print(f"   🔍 {event.get('event_type', 'unknown')}: {event.get('severity_level', 'unknown')}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_model_training():
    """Demo model training"""
    print("\n" + "="*50)
    print("🧠 Model Training Demo")
    print("عرض توضيحي لتدريب النماذج")
    print("="*50)
    
    try:
        from predictive_analytics import RealPredictiveAnalytics
        
        analytics = RealPredictiveAnalytics(output_dir="demo_analytics_output")
        
        print("\n[*] Generating training data...")
        
        # Generate sufficient training data
        for i in range(30):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            if i % 5 == 0:
                analytics.collect_security_events()
            
            if i % 10 == 0:
                print(f"   Progress: {i+1}/30 data points collected")
            
            time.sleep(0.1)
        
        print(f"\n✅ Training data collected:")
        print(f"   System Metrics: {len(analytics.system_metrics)}")
        print(f"   Network Data: {len(analytics.network_data)}")
        print(f"   User Behavior: {len(analytics.user_behavior)}")
        print(f"   Security Events: {len(analytics.security_events)}")
        
        print("\n[*] Training machine learning models...")
        training_success = analytics.train_models()
        
        if training_success:
            print("✅ Model training completed successfully!")
            
            # Show which models were trained
            trained_models = []
            for name, model in analytics.models.items():
                if model is not None:
                    trained_models.append(name)
            
            if trained_models:
                print(f"   Trained models: {', '.join(trained_models)}")
            else:
                print("   No models trained (may need more data)")
        else:
            print("ℹ️  Model training skipped (insufficient data or missing libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_predictions():
    """Demo prediction capabilities"""
    print("\n" + "="*50)
    print("🔮 Predictions Demo")
    print("عرض توضيحي للتنبؤات")
    print("="*50)
    
    try:
        from predictive_analytics import RealPredictiveAnalytics
        
        analytics = RealPredictiveAnalytics(output_dir="demo_analytics_output")
        
        # Generate data and train models
        print("\n[*] Preparing prediction models...")
        for i in range(20):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            analytics.collect_security_events()
            time.sleep(0.1)
        
        analytics.train_models()
        
        print("\n[*] Making predictions...")
        
        # System behavior prediction
        system_pred = analytics.predict_system_behavior()
        if system_pred:
            print(f"✅ System Behavior Prediction:")
            print(f"   Target: {system_pred.get('prediction_target', 'unknown')}")
            print(f"   Predicted Value: {system_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {system_pred.get('confidence_score', 0):.2f}")
            print(f"   Model: {system_pred.get('model_used', 'unknown')}")
        
        # User activity prediction
        user_pred = analytics.predict_user_activity()
        if user_pred:
            print(f"\n✅ User Activity Prediction:")
            print(f"   Target: {user_pred.get('prediction_target', 'unknown')}")
            print(f"   Predicted Value: {user_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {user_pred.get('confidence_score', 0):.2f}")
            print(f"   Model: {user_pred.get('model_used', 'unknown')}")
        
        # Network traffic prediction
        network_pred = analytics.predict_network_traffic()
        if network_pred:
            print(f"\n✅ Network Traffic Prediction:")
            print(f"   Target: {network_pred.get('prediction_target', 'unknown')}")
            print(f"   Predicted Value: {network_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {network_pred.get('confidence_score', 0):.2f}")
            print(f"   Model: {network_pred.get('model_used', 'unknown')}")
        
        # Threat prediction
        threat_pred = analytics.create_threat_prediction()
        if threat_pred:
            print(f"\n✅ Threat Prediction:")
            print(f"   Target: {threat_pred.get('prediction_target', 'unknown')}")
            print(f"   Predicted Value: {threat_pred.get('predicted_value', 'N/A')}")
            print(f"   Confidence: {threat_pred.get('confidence_score', 0):.2f}")
            print(f"   Model: {threat_pred.get('model_used', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_alerts_and_recommendations():
    """Demo alerts and recommendations"""
    print("\n" + "="*50)
    print("🚨 Alerts and Recommendations Demo")
    print("عرض توضيحي للتنبيهات والتوصيات")
    print("="*50)
    
    try:
        from predictive_analytics import RealPredictiveAnalytics
        
        analytics = RealPredictiveAnalytics(output_dir="demo_analytics_output")
        
        # Create sample predictions for demonstration
        sample_predictions = [
            {
                'prediction_type': 'system_behavior',
                'prediction_target': 'cpu_usage',
                'predicted_value': '87.5',
                'confidence_score': 0.85,
                'model_used': 'RandomForestRegressor',
                'prediction_horizon': 5,
                'timestamp': '2024-01-01T12:00:00'
            },
            {
                'prediction_type': 'user_activity',
                'prediction_target': 'activity_level',
                'predicted_value': 'active',
                'confidence_score': 0.92,
                'model_used': 'LogisticRegression',
                'prediction_horizon': 10,
                'timestamp': '2024-01-01T12:01:00'
            },
            {
                'prediction_type': 'threat_prediction',
                'prediction_target': 'threat_level',
                'predicted_value': 'medium',
                'confidence_score': 0.78,
                'model_used': 'rule_based',
                'prediction_horizon': 15,
                'timestamp': '2024-01-01T12:02:00'
            }
        ]
        
        print("\n[*] Generating alerts based on predictions...")
        
        alerts = []
        for i, prediction in enumerate(sample_predictions):
            alert = analytics.generate_alert(prediction)
            if alert:
                alerts.append(alert)
                print(f"\n✅ Alert {i+1}:")
                print(f"   Type: {alert.get('alert_type', 'unknown')}")
                print(f"   Level: {alert.get('alert_level', 'unknown').upper()}")
                print(f"   Message: {alert.get('alert_message', 'N/A')}")
                
                # Show recommended actions
                actions = alert.get('recommended_actions', '[]')
                if actions != '[]':
                    import json
                    try:
                        action_list = json.loads(actions)
                        if action_list:
                            print(f"   Recommended Actions: {', '.join(action_list)}")
                    except:
                        pass
        
        print(f"\nTotal alerts generated: {len(alerts)}")
        
        # Generate recommendations
        print("\n[*] Generating recommendations...")
        recommendations = analytics.generate_recommendations(sample_predictions)
        
        if recommendations:
            print(f"\n✅ Generated {len(recommendations)} recommendations:")
            for i, rec in enumerate(recommendations):
                print(f"\n   📋 Recommendation {i+1}:")
                print(f"      Type: {rec.get('type', 'unknown')}")
                print(f"      Action: {rec.get('action', 'N/A')}")
                print(f"      Reason: {rec.get('reason', 'N/A')}")
                print(f"      Priority: {rec.get('priority', 'unknown').upper()}")
        else:
            print("ℹ️  No specific recommendations generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_comprehensive_analysis():
    """Demo comprehensive predictive analysis"""
    print("\n" + "="*50)
    print("🎯 Comprehensive Analysis Demo")
    print("عرض توضيحي للتحليل الشامل")
    print("="*50)
    
    try:
        from predictive_analytics import RealPredictiveAnalytics
        
        analytics = RealPredictiveAnalytics(output_dir="demo_analytics_output")
        
        print("\n[*] Running comprehensive predictive analysis...")
        
        # Generate data
        for i in range(15):
            analytics.collect_system_metrics()
            analytics.collect_network_data()
            analytics.collect_user_behavior()
            if i % 3 == 0:
                analytics.collect_security_events()
            time.sleep(0.1)
        
        # Train models
        analytics.train_models()
        
        # Run comprehensive analysis
        results = analytics.run_predictive_analysis()
        
        if results:
            print(f"✅ Comprehensive analysis completed!")
            print(f"   Analysis Timestamp: {results.get('timestamp', 'N/A')}")
            
            # Show predictions summary
            predictions = results.get('predictions', [])
            print(f"\n📊 Predictions Summary ({len(predictions)} total):")
            for pred in predictions:
                pred_type = pred.get('prediction_type', 'unknown')
                pred_value = pred.get('predicted_value', 'N/A')
                confidence = pred.get('confidence_score', 0)
                print(f"   🔮 {pred_type}: {pred_value} (confidence: {confidence:.2f})")
            
            # Show alerts summary
            alerts = results.get('alerts', [])
            print(f"\n🚨 Alerts Summary ({len(alerts)} total):")
            for alert in alerts:
                alert_level = alert.get('alert_level', 'unknown')
                alert_msg = alert.get('alert_message', 'N/A')
                print(f"   ⚠️  {alert_level.upper()}: {alert_msg}")
            
            # Show recommendations summary
            recommendations = results.get('recommendations', [])
            print(f"\n📋 Recommendations Summary ({len(recommendations)} total):")
            for rec in recommendations:
                rec_type = rec.get('type', 'unknown')
                rec_action = rec.get('action', 'N/A')
                rec_priority = rec.get('priority', 'unknown')
                print(f"   📌 {rec_type}: {rec_action} (priority: {rec_priority})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main demo function"""
    print("🎭 Predictive Analytics Module Demo")
    print("عرض توضيحي لوحدة التحليلات التنبؤية")
    print("="*60)
    print("⚠️  This is a demonstration of predictive analytics capabilities")
    print("⚠️  هذا عرض توضيحي لقدرات التحليلات التنبؤية")
    print("="*60)
    
    # Run demos
    demos = [
        ("Data Collection", demo_data_collection),
        ("Model Training", demo_model_training),
        ("Predictions", demo_predictions),
        ("Alerts and Recommendations", demo_alerts_and_recommendations),
        ("Comprehensive Analysis", demo_comprehensive_analysis)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n🔄 Running {demo_name} demo...")
        try:
            results[demo_name] = demo_func()
        except Exception as e:
            print(f"❌ {demo_name} demo failed: {e}")
            results[demo_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEMO RESULTS SUMMARY")
    print("ملخص نتائج العرض التوضيحي")
    print("="*60)
    
    passed = 0
    total = len(demos)
    
    for demo_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} demos successful")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All demos completed successfully!")
        print("🎉 جميع العروض التوضيحية اكتملت بنجاح!")
        print("\nThe Predictive Analytics module is working correctly.")
        print("وحدة التحليلات التنبؤية تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} demo(s) failed.")
        print(f"⚠️  {total-passed} عرض توضيحي فشل.")
        print("\nSome features may require additional libraries.")
        print("بعض الميزات قد تتطلب مكتبات إضافية.")
    
    print(f"\n📁 Demo output saved to: demo_analytics_output/")
    print(f"📁 مخرجات العرض التوضيحي محفوظة في: demo_analytics_output/")

if __name__ == "__main__":
    main()
