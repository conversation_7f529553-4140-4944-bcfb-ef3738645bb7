#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات الوحدات الأساسية
Core Modules Tests
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path
import json
import yaml

# إضافة مسار المشروع
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config_manager import ConfigManager
from core.database_manager import DatabaseManager
from core.security_manager import SecurityManager
from core.real_data_generator import RealDataGenerator
from core.base_module import BaseSecurityModule, ModuleType

class TestConfigManager(unittest.TestCase):
    """اختبارات مدير الإعدادات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.config_manager = ConfigManager()
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        os.rmdir(self.temp_dir)
    
    def test_default_config_loading(self):
        """اختبار تحميل الإعدادات الافتراضية"""
        framework_name = self.config_manager.get("general.framework_name")
        self.assertEqual(framework_name, "Security Exploitation Framework")
    
    def test_config_get_set(self):
        """اختبار الحصول على وتعيين الإعدادات"""
        # تعيين قيمة
        self.assertTrue(self.config_manager.set("test.key", "test_value"))
        
        # الحصول على القيمة
        value = self.config_manager.get("test.key")
        self.assertEqual(value, "test_value")
        
        # الحصول على قيمة غير موجودة
        value = self.config_manager.get("nonexistent.key", "default")
        self.assertEqual(value, "default")
    
    def test_config_file_operations(self):
        """اختبار عمليات ملف الإعدادات"""
        # إنشاء ملف إعدادات تجريبي
        test_config = {
            "test_section": {
                "test_key": "test_value",
                "test_number": 42
            }
        }
        
        with open(self.config_file, 'w') as f:
            yaml.dump(test_config, f)
        
        # تحميل الملف
        self.assertTrue(self.config_manager.load_config(self.config_file))
        
        # التحقق من القيم
        self.assertEqual(self.config_manager.get("test_section.test_key"), "test_value")
        self.assertEqual(self.config_manager.get("test_section.test_number"), 42)
    
    def test_config_validation(self):
        """اختبار التحقق من صحة الإعدادات"""
        validation_result = self.config_manager.validate_config()
        self.assertIsInstance(validation_result, dict)
        self.assertIn("valid", validation_result)
        self.assertIn("errors", validation_result)
        self.assertIn("warnings", validation_result)

class TestDatabaseManager(unittest.TestCase):
    """اختبارات مدير قواعد البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_manager = DatabaseManager()
        # استخدام قاعدة بيانات مؤقتة للاختبار
        self.db_manager.db_path = Path(self.temp_dir) / "test.db"
        self.db_manager._create_tables()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if self.db_manager.db_path.exists():
            self.db_manager.db_path.unlink()
        os.rmdir(self.temp_dir)
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        self.assertTrue(self.db_manager.db_path.exists())
    
    def test_session_operations(self):
        """اختبار عمليات الجلسات"""
        # إنشاء جلسة
        session_id = self.db_manager.create_session("test_user", "127.0.0.1")
        self.assertIsNotNone(session_id)
        
        # الحصول على الجلسة
        session = self.db_manager.get_session(session_id)
        self.assertIsNotNone(session)
        self.assertEqual(session['user_id'], "test_user")
        
        # تحديث نشاط الجلسة
        self.db_manager.update_session_activity(session_id)
        
        # إغلاق الجلسة
        self.db_manager.close_session(session_id)
        
        # التحقق من إغلاق الجلسة
        session = self.db_manager.get_session(session_id)
        self.assertEqual(session['status'], 'closed')
    
    def test_campaign_operations(self):
        """اختبار عمليات الحملات"""
        # إنشاء حملة
        campaign_id = self.db_manager.create_campaign(
            "Test Campaign", 
            "Test Description", 
            "test_user",
            ["target1", "target2"],
            ["module1", "module2"]
        )
        self.assertIsNotNone(campaign_id)
    
    def test_vulnerability_storage(self):
        """اختبار حفظ الثغرات"""
        vuln_data = {
            "cve_id": "CVE-2023-0001",
            "description": "Test vulnerability",
            "severity": 7.5,
            "cvss_score": 7.5,
            "published_date": "2023-01-01",
            "affected_products": ["Product A", "Product B"],
            "references": ["http://example.com"]
        }
        
        result = self.db_manager.store_vulnerability(vuln_data)
        self.assertTrue(result)
    
    def test_database_statistics(self):
        """اختبار إحصائيات قاعدة البيانات"""
        stats = self.db_manager.get_database_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn("sessions_count", stats)
        self.assertIn("campaigns_count", stats)

class TestSecurityManager(unittest.TestCase):
    """اختبارات مدير الأمان"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.temp_dir = tempfile.mkdtemp()
        self.security_manager = SecurityManager()
        self.security_manager.keys_dir = Path(self.temp_dir)
        self.security_manager._initialize_encryption()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # حذف الملفات المؤقتة
        for file in Path(self.temp_dir).glob("*"):
            file.unlink()
        os.rmdir(self.temp_dir)
    
    def test_encryption_decryption(self):
        """اختبار التشفير وفك التشفير"""
        original_data = "This is a test message"
        
        # تشفير البيانات
        encrypted_data = self.security_manager.encrypt_data(original_data)
        self.assertNotEqual(encrypted_data, original_data)
        
        # فك التشفير
        decrypted_data = self.security_manager.decrypt_data(encrypted_data)
        self.assertEqual(decrypted_data, original_data)
    
    def test_password_hashing(self):
        """اختبار تشفير كلمات المرور"""
        password = "test_password123"
        
        # تشفير كلمة المرور
        hashed_password, salt = self.security_manager.hash_password(password)
        self.assertNotEqual(hashed_password, password)
        self.assertIsNotNone(salt)
        
        # التحقق من كلمة المرور
        self.assertTrue(self.security_manager.verify_password(password, hashed_password, salt))
        self.assertFalse(self.security_manager.verify_password("wrong_password", hashed_password, salt))
    
    def test_session_key_management(self):
        """اختبار إدارة مفاتيح الجلسات"""
        session_id = "test_session_123"
        
        # توليد مفتاح جلسة
        session_key = self.security_manager.generate_session_key(session_id)
        self.assertIsNotNone(session_key)
        
        # الحصول على مفتاح الجلسة
        retrieved_key = self.security_manager.get_session_key(session_id)
        self.assertEqual(session_key, retrieved_key)
        
        # إلغاء مفتاح الجلسة
        self.security_manager.revoke_session_key(session_id)
        revoked_key = self.security_manager.get_session_key(session_id)
        self.assertIsNone(revoked_key)
    
    def test_api_key_management(self):
        """اختبار إدارة مفاتيح API"""
        user_id = "test_user"
        permissions = ["read", "write"]
        
        # توليد مفتاح API
        api_key = self.security_manager.generate_api_key(user_id, permissions)
        self.assertIsNotNone(api_key)
        
        # التحقق من صحة مفتاح API
        key_data = self.security_manager.validate_api_key(api_key, "read")
        self.assertIsNotNone(key_data)
        self.assertEqual(key_data["user_id"], user_id)
        
        # التحقق من صلاحية غير موجودة
        invalid_key_data = self.security_manager.validate_api_key(api_key, "admin")
        self.assertIsNone(invalid_key_data)
        
        # إلغاء مفتاح API
        self.security_manager.revoke_api_key(api_key)
        revoked_key_data = self.security_manager.validate_api_key(api_key)
        self.assertIsNone(revoked_key_data)
    
    def test_rate_limiting(self):
        """اختبار حد المعدل"""
        identifier = "test_user"
        
        # يجب أن يسمح بالطلبات الأولى
        self.assertTrue(self.security_manager.check_rate_limit(identifier, max_requests=3, window_minutes=1))
        self.assertTrue(self.security_manager.check_rate_limit(identifier, max_requests=3, window_minutes=1))
        self.assertTrue(self.security_manager.check_rate_limit(identifier, max_requests=3, window_minutes=1))
        
        # يجب أن يرفض الطلب الرابع
        self.assertFalse(self.security_manager.check_rate_limit(identifier, max_requests=3, window_minutes=1))
    
    def test_security_statistics(self):
        """اختبار إحصائيات الأمان"""
        stats = self.security_manager.get_security_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn("encryption_enabled", stats)
        self.assertIn("master_key_exists", stats)

class TestRealDataGenerator(unittest.TestCase):
    """اختبارات مولد البيانات الحقيقية"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.temp_dir = tempfile.mkdtemp()
        config = {"cache_dir": self.temp_dir}
        self.data_generator = RealDataGenerator(config)
        self.data_generator.cache_dir = Path(self.temp_dir)
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # حذف الملفات المؤقتة
        for file in Path(self.temp_dir).glob("*"):
            file.unlink()
        os.rmdir(self.temp_dir)
    
    def test_network_traffic_generation(self):
        """اختبار توليد حركة مرور الشبكة"""
        traffic_data = self.data_generator.generate_realistic_network_traffic(duration_hours=2)
        
        self.assertIsInstance(traffic_data, list)
        self.assertEqual(len(traffic_data), 2)  # ساعتان
        
        # التحقق من بنية البيانات
        if traffic_data:
            entry = traffic_data[0]
            self.assertIn("timestamp", entry)
            self.assertIn("total_packets", entry)
            self.assertIn("protocols", entry)
    
    def test_user_behavior_generation(self):
        """اختبار توليد سلوك المستخدمين"""
        user_data = self.data_generator.create_realistic_user_behavior(num_users=5, days=3)
        
        self.assertIsInstance(user_data, list)
        self.assertEqual(len(user_data), 5)  # 5 مستخدمين
        
        # التحقق من بنية البيانات
        if user_data:
            user = user_data[0]
            self.assertIn("user_id", user)
            self.assertIn("department", user)
            self.assertIn("login_patterns", user)
    
    def test_attack_scenarios_simulation(self):
        """اختبار محاكاة سيناريوهات الهجوم"""
        scenarios = self.data_generator.simulate_attack_scenarios()
        
        self.assertIsInstance(scenarios, list)
        self.assertGreater(len(scenarios), 0)
        
        # التحقق من بنية البيانات
        if scenarios:
            scenario = scenarios[0]
            self.assertIn("name", scenario)
            self.assertIn("type", scenario)
            self.assertIn("timeline", scenario)
    
    def test_data_caching(self):
        """اختبار التخزين المؤقت للبيانات"""
        test_data = {"test": "data"}
        
        # حفظ البيانات
        self.data_generator._cache_data("test_type", test_data)
        
        # تحميل البيانات
        loaded_data = self.data_generator._load_cached_data("test_type")
        self.assertEqual(loaded_data, test_data)
    
    def test_data_statistics(self):
        """اختبار إحصائيات البيانات"""
        stats = self.data_generator.get_data_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn("cache_directory", stats)
        self.assertIn("total_cached_files", stats)

class TestBaseModule(unittest.TestCase):
    """اختبارات الوحدة الأساسية"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء وحدة تجريبية
        class TestModule(BaseSecurityModule):
            def validate_target(self, target: str) -> bool:
                return target == "valid_target"
            
            def execute(self, target: str, **kwargs):
                return {"success": True, "target": target}
        
        self.test_module = TestModule("test_module", ModuleType.RECONNAISSANCE)
    
    def test_module_initialization(self):
        """اختبار تهيئة الوحدة"""
        self.assertEqual(self.test_module.name, "test_module")
        self.assertEqual(self.test_module.module_type, ModuleType.RECONNAISSANCE)
        self.assertIsNotNone(self.test_module.module_id)
    
    def test_authorization(self):
        """اختبار التصريح"""
        # بدون تصريح
        self.assertFalse(self.test_module.authorized)
        
        # مع التصريح
        self.test_module.set_authorization(True, True)
        self.assertTrue(self.test_module.authorized)
        self.assertTrue(self.test_module.target_authorized)
    
    def test_execution_with_authorization(self):
        """اختبار التنفيذ مع التصريح"""
        # تعيين التصريح
        self.test_module.set_authorization(True, True)
        
        # تنفيذ ناجح
        result = self.test_module.start_execution("valid_target")
        self.assertTrue(result["success"])
        
        # تنفيذ فاشل (هدف غير صحيح)
        result = self.test_module.start_execution("invalid_target")
        self.assertFalse(result["success"])
    
    def test_execution_without_authorization(self):
        """اختبار التنفيذ بدون تصريح"""
        result = self.test_module.start_execution("valid_target")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
    
    def test_module_status(self):
        """اختبار حالة الوحدة"""
        status = self.test_module.get_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn("module_id", status)
        self.assertIn("name", status)
        self.assertIn("status", status)
        self.assertIn("progress", status)

if __name__ == "__main__":
    # تشغيل جميع الاختبارات
    unittest.main(verbosity=2)
