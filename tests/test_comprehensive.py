#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات شاملة لإطار عمل الأمان
Comprehensive Tests for Security Framework
"""

import unittest
import sys
import os
import json
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# إضافة مسار المشروع للاستيراد
sys.path.append(str(os.path.dirname(os.path.dirname(__file__))))

# استيراد الوحدات المطلوب اختبارها
from core.config_manager import config
from core.database_manager import db_manager
from core.security_manager import security_manager
from modules.reconnaissance.basic_recon import BasicReconModule
from modules.reconnaissance.advanced_osint import AdvancedOSINTModule
from modules.exploitation.web_exploitation import WebExploitationModule
from modules.intelligence.ai_engine import AISecurityEngine
from api.rest_api import SecurityFrameworkAPI

class TestCoreComponents(unittest.TestCase):
    """اختبارات المكونات الأساسية"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_config = {
            'database': {
                'type': 'sqlite',
                'path': ':memory:'
            },
            'security': {
                'encryption_key': 'test_key_123456789012345678901234',
                'api_key_length': 32
            }
        }
    
    def test_config_manager(self):
        """اختبار مدير الإعدادات"""
        # اختبار تحميل الإعدادات
        self.assertTrue(config.load_config_from_dict(self.test_config))
        
        # اختبار الحصول على القيم
        self.assertEqual(config.get('database.type'), 'sqlite')
        self.assertEqual(config.get('security.api_key_length'), 32)
        
        # اختبار القيم الافتراضية
        self.assertEqual(config.get('nonexistent.key', 'default'), 'default')
    
    def test_database_manager(self):
        """اختبار مدير قاعدة البيانات"""
        # تهيئة قاعدة البيانات
        db_manager.initialize_database(':memory:')
        
        # اختبار إنشاء جلسة
        session_id = db_manager.create_session('test_user')
        self.assertIsNotNone(session_id)
        self.assertEqual(len(session_id), 36)  # UUID length
        
        # اختبار الحصول على الجلسة
        session = db_manager.get_session(session_id)
        self.assertIsNotNone(session)
        self.assertEqual(session['user_id'], 'test_user')
        
        # اختبار حفظ النتائج
        test_result = {'test': 'data', 'timestamp': datetime.now().isoformat()}
        result_id = db_manager.save_module_result(
            session_id, 'test_module', 'test_category', 'test_target', test_result
        )
        self.assertIsNotNone(result_id)
        
        # اختبار الحصول على النتائج
        results = db_manager.get_session_results(session_id)
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)
        
        # اختبار الإحصائيات
        stats = db_manager.get_database_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_sessions', stats)
    
    def test_security_manager(self):
        """اختبار مدير الأمان"""
        # اختبار توليد مفتاح API
        api_key = security_manager.generate_api_key('test_user', ['read', 'write'])
        self.assertIsNotNone(api_key)
        self.assertIsInstance(api_key, str)
        
        # اختبار التحقق من مفتاح API
        key_data = security_manager.validate_api_key(api_key)
        self.assertIsNotNone(key_data)
        self.assertEqual(key_data['user_id'], 'test_user')
        self.assertIn('read', key_data['permissions'])
        
        # اختبار التشفير
        test_data = "sensitive information"
        encrypted = security_manager.encrypt_data(test_data)
        self.assertNotEqual(encrypted, test_data)
        
        # اختبار فك التشفير
        decrypted = security_manager.decrypt_data(encrypted)
        self.assertEqual(decrypted, test_data)
        
        # اختبار توليد رمز آمن
        token = security_manager.generate_secure_token()
        self.assertIsNotNone(token)
        self.assertGreater(len(token), 20)

class TestReconnaissanceModules(unittest.TestCase):
    """اختبارات وحدات الاستطلاع"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_target_ip = "*******"
        self.test_target_domain = "example.com"
        self.test_target_email = "<EMAIL>"
    
    def test_basic_reconnaissance(self):
        """اختبار الاستطلاع الأساسي"""
        recon_module = BasicReconModule()
        recon_module.set_authorization(True, True)
        
        # اختبار التحقق من الهدف
        self.assertTrue(recon_module.validate_target(self.test_target_ip))
        self.assertTrue(recon_module.validate_target(self.test_target_domain))
        self.assertFalse(recon_module.validate_target("invalid_target"))
        
        # اختبار تنفيذ الاستطلاع على IP
        with patch('socket.gethostbyaddr') as mock_gethostbyaddr:
            mock_gethostbyaddr.return_value = ('dns.google', [], ['*******'])
            
            results = recon_module.start_execution(self.test_target_ip)
            
            self.assertIsInstance(results, dict)
            self.assertTrue(results.get('success', False))
            self.assertIn('data', results)
            self.assertIn('target_info', results['data'])
    
    @patch('requests.get')
    def test_advanced_osint(self, mock_get):
        """اختبار OSINT المتقدم"""
        # إعداد mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'ip_str': '*******', 'country_name': 'United States'}
        mock_get.return_value = mock_response
        
        osint_module = AdvancedOSINTModule({
            'shodan_api_key': 'test_key',
            'virustotal_api_key': 'test_key'
        })
        osint_module.set_authorization(True, True)
        
        # اختبار التحقق من الهدف
        self.assertTrue(osint_module.validate_target(self.test_target_ip))
        self.assertTrue(osint_module.validate_target(self.test_target_email))
        
        # اختبار تنفيذ OSINT
        results = osint_module.start_execution(self.test_target_ip)
        
        self.assertIsInstance(results, dict)
        self.assertIn('data', results)

class TestExploitationModules(unittest.TestCase):
    """اختبارات وحدات الاستغلال"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_url = "http://testphp.vulnweb.com/"
    
    @patch('requests.get')
    def test_web_exploitation(self, mock_get):
        """اختبار استغلال الويب"""
        # إعداد mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html><body>Test Page</body></html>"
        mock_response.headers = {'Server': 'Apache/2.4.41'}
        mock_get.return_value = mock_response
        
        exploit_module = WebExploitationModule()
        exploit_module.set_authorization(True, True)
        
        # اختبار التحقق من الهدف
        self.assertTrue(exploit_module.validate_target(self.test_url))
        self.assertFalse(exploit_module.validate_target("invalid_url"))
        
        # اختبار تنفيذ الاستغلال
        results = exploit_module.start_execution(self.test_url)
        
        self.assertIsInstance(results, dict)
        self.assertIn('data', results)
        self.assertIn('site_info', results['data'])

class TestAIEngine(unittest.TestCase):
    """اختبارات محرك الذكاء الاصطناعي"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.ai_engine = AISecurityEngine()
        self.ai_engine.set_authorization(True, True)
        
        self.test_vulnerability_data = {
            'vulnerabilities': [
                {
                    'cve_id': 'CVE-2023-0001',
                    'cvss_score': 9.8,
                    'exploit_available': True,
                    'published_date': '2023-01-01T00:00:00Z',
                    'description': 'Remote code execution vulnerability'
                }
            ]
        }
        
        self.test_exploit_data = {
            'target_type': 'web_application',
            'defenses': ['firewall'],
            'attacker_skill': 'intermediate',
            'exploit_complexity': 'low'
        }
    
    def test_vulnerability_assessment(self):
        """اختبار تقييم الثغرات"""
        results = self.ai_engine.start_execution(
            "vulnerability_assessment",
            vulnerability_data=self.test_vulnerability_data
        )
        
        self.assertIsInstance(results, dict)
        self.assertTrue(results.get('success', False))
        self.assertIn('vulnerability_assessment', results['data'])
        
        assessment = results['data']['vulnerability_assessment']
        self.assertIn('total_vulnerabilities', assessment)
        self.assertIn('risk_score', assessment)
        self.assertEqual(assessment['total_vulnerabilities'], 1)
    
    def test_exploit_prediction(self):
        """اختبار التنبؤ بالاستغلال"""
        results = self.ai_engine.start_execution(
            "exploit_prediction",
            exploit_data=self.test_exploit_data
        )
        
        self.assertIsInstance(results, dict)
        self.assertTrue(results.get('success', False))
        self.assertIn('exploit_prediction', results['data'])
        
        prediction = results['data']['exploit_prediction']
        self.assertIn('success_probability', prediction)
        self.assertIn('confidence', prediction)
        self.assertIsInstance(prediction['success_probability'], float)
    
    def test_comprehensive_analysis(self):
        """اختبار التحليل الشامل"""
        results = self.ai_engine.start_execution(
            "comprehensive_analysis",
            vulnerability_data=self.test_vulnerability_data,
            exploit_data=self.test_exploit_data
        )
        
        self.assertIsInstance(results, dict)
        self.assertTrue(results.get('success', False))
        self.assertIn('vulnerability_assessment', results['data'])
        self.assertIn('exploit_prediction', results['data'])
        self.assertIn('recommendations', results['data'])

class TestAPIEndpoints(unittest.TestCase):
    """اختبارات واجهات برمجة التطبيقات"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.api = SecurityFrameworkAPI()
        self.client = self.api.app.test_client()
        self.api.app.config['TESTING'] = True
        
        # الحصول على مفتاح API للاختبار
        with self.client:
            response = self.client.post('/api/auth/login', 
                                      json={'username': 'admin', 'password': 'admin123'})
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.api_key = data['api_key']
            self.headers = {'Authorization': f'Bearer {self.api_key}'}
    
    def test_authentication(self):
        """اختبار المصادقة"""
        # اختبار تسجيل الدخول الصحيح
        response = self.client.post('/api/auth/login', 
                                  json={'username': 'admin', 'password': 'admin123'})
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('api_key', data)
        
        # اختبار تسجيل الدخول الخاطئ
        response = self.client.post('/api/auth/login', 
                                  json={'username': 'admin', 'password': 'wrong'})
        self.assertEqual(response.status_code, 401)
    
    def test_session_management(self):
        """اختبار إدارة الجلسات"""
        # إنشاء جلسة
        response = self.client.post('/api/sessions', headers=self.headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        session_id = data['session_id']
        
        # الحصول على معلومات الجلسة
        response = self.client.get(f'/api/sessions/{session_id}', headers=self.headers)
        self.assertEqual(response.status_code, 200)
        
        # إغلاق الجلسة
        response = self.client.delete(f'/api/sessions/{session_id}', headers=self.headers)
        self.assertEqual(response.status_code, 200)
    
    @patch('threading.Thread')
    def test_reconnaissance_endpoint(self, mock_thread):
        """اختبار نقطة نهاية الاستطلاع"""
        # إعداد mock thread
        mock_thread_instance = Mock()
        mock_thread.return_value = mock_thread_instance
        
        response = self.client.post('/api/reconnaissance/basic',
                                  json={'target': '*******'},
                                  headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('operation_id', data)
        self.assertEqual(data['status'], 'started')
        
        # التحقق من تشغيل الخيط
        mock_thread.assert_called_once()
        mock_thread_instance.start.assert_called_once()
    
    def test_health_check(self):
        """اختبار فحص الصحة"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('status', data)
        self.assertIn('components', data)
    
    def test_unauthorized_access(self):
        """اختبار الوصول غير المصرح به"""
        # محاولة الوصول بدون مصادقة
        response = self.client.post('/api/sessions')
        self.assertEqual(response.status_code, 401)
        
        # محاولة الوصول بمفتاح خاطئ
        wrong_headers = {'Authorization': 'Bearer wrong_key'}
        response = self.client.post('/api/sessions', headers=wrong_headers)
        self.assertEqual(response.status_code, 401)

class TestIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # تهيئة قاعدة البيانات في الذاكرة
        db_manager.initialize_database(':memory:')
        
        # إنشاء جلسة اختبار
        self.session_id = db_manager.create_session('integration_test_user')
    
    def test_full_reconnaissance_workflow(self):
        """اختبار سير عمل الاستطلاع الكامل"""
        # إنشاء وحدة الاستطلاع
        recon_module = BasicReconModule()
        recon_module.set_authorization(True, True)
        recon_module.set_session(self.session_id)
        
        # تنفيذ الاستطلاع
        with patch('socket.gethostbyaddr') as mock_gethostbyaddr:
            mock_gethostbyaddr.return_value = ('dns.google', [], ['*******'])
            
            results = recon_module.start_execution('*******')
            
            # التحقق من النتائج
            self.assertTrue(results.get('success', False))
            
            # التحقق من حفظ النتائج في قاعدة البيانات
            session_results = db_manager.get_session_results(self.session_id)
            self.assertGreater(len(session_results), 0)
    
    def test_ai_analysis_workflow(self):
        """اختبار سير عمل التحليل الذكي"""
        # إنشاء بيانات اختبار
        test_data = {
            'vulnerability_data': {
                'vulnerabilities': [
                    {
                        'cve_id': 'CVE-2023-TEST',
                        'cvss_score': 8.5,
                        'exploit_available': True,
                        'description': 'Test vulnerability'
                    }
                ]
            }
        }
        
        # تنفيذ التحليل الذكي
        ai_engine = AISecurityEngine()
        ai_engine.set_authorization(True, True)
        ai_engine.set_session(self.session_id)
        
        results = ai_engine.start_execution('vulnerability_assessment', **test_data)
        
        # التحقق من النتائج
        self.assertTrue(results.get('success', False))
        self.assertIn('vulnerability_assessment', results['data'])

class TestPerformance(unittest.TestCase):
    """اختبارات الأداء"""
    
    def test_database_performance(self):
        """اختبار أداء قاعدة البيانات"""
        db_manager.initialize_database(':memory:')
        
        start_time = time.time()
        
        # إنشاء عدة جلسات
        session_ids = []
        for i in range(100):
            session_id = db_manager.create_session(f'user_{i}')
            session_ids.append(session_id)
        
        # حفظ نتائج متعددة
        for session_id in session_ids[:10]:
            for j in range(10):
                db_manager.save_module_result(
                    session_id, f'module_{j}', 'test_category',
                    f'target_{j}', {'test': 'data'}
                )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # يجب أن يكتمل في أقل من 5 ثوان
        self.assertLess(execution_time, 5.0)
        
        # التحقق من الإحصائيات
        stats = db_manager.get_database_statistics()
        self.assertEqual(stats['total_sessions'], 100)
        self.assertEqual(stats['total_results'], 100)
    
    def test_concurrent_operations(self):
        """اختبار العمليات المتزامنة"""
        db_manager.initialize_database(':memory:')
        
        results = []
        threads = []
        
        def create_session_and_save_result(user_id):
            try:
                session_id = db_manager.create_session(f'user_{user_id}')
                result_id = db_manager.save_module_result(
                    session_id, 'test_module', 'test_category',
                    'test_target', {'user_id': user_id}
                )
                results.append((session_id, result_id))
            except Exception as e:
                results.append(('error', str(e)))
        
        # إنشاء 10 خيوط متزامنة
        for i in range(10):
            thread = threading.Thread(target=create_session_and_save_result, args=(i,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        # التحقق من النتائج
        self.assertEqual(len(results), 10)
        
        # التحقق من عدم وجود أخطاء
        errors = [r for r in results if r[0] == 'error']
        self.assertEqual(len(errors), 0)

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات المكونات الأساسية
    test_suite.addTest(unittest.makeSuite(TestCoreComponents))
    
    # إضافة اختبارات وحدات الاستطلاع
    test_suite.addTest(unittest.makeSuite(TestReconnaissanceModules))
    
    # إضافة اختبارات وحدات الاستغلال
    test_suite.addTest(unittest.makeSuite(TestExploitationModules))
    
    # إضافة اختبارات محرك الذكاء الاصطناعي
    test_suite.addTest(unittest.makeSuite(TestAIEngine))
    
    # إضافة اختبارات API
    test_suite.addTest(unittest.makeSuite(TestAPIEndpoints))
    
    # إضافة اختبارات التكامل
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # إضافة اختبارات الأداء
    test_suite.addTest(unittest.makeSuite(TestPerformance))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result

if __name__ == '__main__':
    print("🧪 بدء الاختبارات الشاملة لإطار عمل الأمان")
    print("=" * 60)
    
    result = run_all_tests()
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبارات:")
    print(f"✅ اختبارات نجحت: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ اختبارات فشلت: {len(result.failures)}")
    print(f"🚫 أخطاء: {len(result.errors)}")
    print(f"📈 معدل النجاح: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print("\n🚫 الأخطاء:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    # إنهاء البرنامج بكود الخطأ المناسب
    exit_code = 0 if result.wasSuccessful() else 1
    sys.exit(exit_code)
