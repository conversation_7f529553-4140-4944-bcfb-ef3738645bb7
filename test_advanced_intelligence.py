#!/usr/bin/env python3
"""
Test script for Advanced Intelligence Module
سكربت اختبار لوحدة الذكاء المتقدم

This script tests the real AI-powered intelligence capabilities.
هذا السكربت يختبر قدرات الذكاء الاصطناعي الحقيقية.

WARNING: FOR AUTHORIZED TESTING ONLY
تحذير: للاختبارات المصرح بها فقط
"""

import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from advanced_intelligence import RealAdvancedIntelligence
except ImportError as e:
    print(f"[-] Error importing advanced_intelligence module: {e}")
    sys.exit(1)


def test_behavioral_data_collection():
    """Test behavioral data collection"""
    print("\n" + "="*50)
    print("📊 Testing Behavioral Data Collection")
    print("اختبار جمع بيانات السلوك")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        print("\n[*] Collecting behavioral data...")
        behavioral_data = ai_system.collect_behavioral_data()
        
        if behavioral_data:
            print(f"✅ Behavioral data collected successfully!")
            print(f"   CPU Usage: {behavioral_data.get('cpu_percent', 0):.2f}%")
            print(f"   Memory Usage: {behavioral_data.get('memory_percent', 0):.2f}%")
            print(f"   Active Processes: {behavioral_data.get('active_processes', 0)}")
            print(f"   Network Connections: {behavioral_data.get('network_connections', 0)}")
            print(f"   User Activity Score: {behavioral_data.get('user_activity_score', 0):.2f}")
            print(f"   Anomaly Score: {behavioral_data.get('anomaly_score', 0):.3f}")
            return True
        else:
            print("❌ Failed to collect behavioral data")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_threat_indicators():
    """Test threat indicators collection"""
    print("\n" + "="*50)
    print("🚨 Testing Threat Indicators Collection")
    print("اختبار جمع مؤشرات التهديد")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        print("\n[*] Collecting threat indicators...")
        threat_indicators = ai_system.collect_threat_indicators()
        
        if threat_indicators:
            print(f"✅ Found {len(threat_indicators)} threat indicators:")
            for indicator in threat_indicators[:5]:  # Show first 5
                print(f"   🔍 {indicator.get('type', 'unknown')}: {indicator.get('value', 'N/A')}")
                print(f"      Confidence: {indicator.get('confidence', 0):.2f}, Level: {indicator.get('threat_level', 'unknown')}")
        else:
            print("ℹ️  No threat indicators detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_ai_predictions():
    """Test AI predictions"""
    print("\n" + "="*50)
    print("🤖 Testing AI Predictions")
    print("اختبار تنبؤات الذكاء الاصطناعي")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        # Collect some data first
        print("\n[*] Collecting initial data for predictions...")
        for i in range(5):
            ai_system.collect_behavioral_data()
            ai_system.collect_threat_indicators()
            time.sleep(1)
        
        print("\n[*] Making AI predictions...")
        predictions = ai_system.predict_threats()
        
        if predictions:
            print(f"✅ Generated {len(predictions)} predictions:")
            for prediction in predictions:
                print(f"   🔮 {prediction.get('type', 'unknown')}: {prediction.get('value', 'N/A')}")
                print(f"      Confidence: {prediction.get('confidence', 0):.3f}, Model: {prediction.get('model', 'unknown')}")
        else:
            print("ℹ️  No predictions generated (may need more data)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_intelligent_decisions():
    """Test intelligent decision making"""
    print("\n" + "="*50)
    print("🧠 Testing Intelligent Decision Making")
    print("اختبار اتخاذ القرارات الذكية")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        # Create mock predictions for testing
        mock_predictions = [
            {
                'type': 'anomaly_detection',
                'value': 'System anomaly detected (score: 0.85)',
                'confidence': 0.85,
                'model': 'IsolationForest',
                'timestamp': '2024-01-01T12:00:00'
            },
            {
                'type': 'ml_threat_prediction',
                'value': 'Threat level: high (score: 0.75)',
                'confidence': 0.75,
                'model': 'custom_threat_model',
                'timestamp': '2024-01-01T12:01:00'
            }
        ]
        
        print("\n[*] Making intelligent decisions based on predictions...")
        decisions = ai_system.make_intelligent_decisions(mock_predictions)
        
        if decisions:
            print(f"✅ Made {len(decisions)} intelligent decisions:")
            for decision in decisions:
                print(f"   ⚡ Action: {decision.get('action', 'unknown')}")
                print(f"      Reason: {decision.get('reason', 'N/A')}")
                print(f"      Priority: {decision.get('priority', 'unknown')}")
        else:
            print("ℹ️  No decisions made")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_nlp_analysis():
    """Test Natural Language Processing"""
    print("\n" + "="*50)
    print("📝 Testing Natural Language Processing")
    print("اختبار معالجة اللغة الطبيعية")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        print("\n[*] Running NLP analysis...")
        nlp_results = ai_system.natural_language_processing()
        
        if nlp_results:
            print(f"✅ NLP analysis completed with {len(nlp_results)} results:")
            for result in nlp_results[:3]:  # Show first 3
                print(f"   📄 Source: {result.get('source', 'unknown')}")
                print(f"      Classification: {result.get('classification', 'unknown')}")
                print(f"      Threat Keywords: {len(result.get('threat_keywords', []))}")
                print(f"      Sentiment: {result.get('sentiment_score', 0):.2f}")
        else:
            print("ℹ️  No NLP results (may require NLP libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_computer_vision():
    """Test Computer Vision analysis"""
    print("\n" + "="*50)
    print("👁️ Testing Computer Vision Analysis")
    print("اختبار تحليل رؤية الكمبيوتر")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        print("\n[*] Running computer vision analysis...")
        cv_results = ai_system.computer_vision_analysis()
        
        if cv_results:
            print(f"✅ Computer vision analysis completed with {len(cv_results)} results:")
            for result in cv_results:
                print(f"   🖼️ Image: {result.get('image_path', 'unknown')}")
                print(f"      Extracted Text Length: {result.get('text_length', 0)}")
                print(f"      Security Indicators: {len(result.get('security_indicators', []))}")
                print(f"      Detected Objects: {len(result.get('detected_objects', []))}")
        else:
            print("ℹ️  No computer vision results (may require CV libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_model_updates():
    """Test AI model updates"""
    print("\n" + "="*50)
    print("🔄 Testing AI Model Updates")
    print("اختبار تحديث نماذج الذكاء الاصطناعي")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        # Generate some data for model training
        print("\n[*] Generating training data...")
        for i in range(20):
            ai_system.collect_behavioral_data()
            if i % 5 == 0:
                ai_system.collect_threat_indicators()
            time.sleep(0.1)
        
        print("\n[*] Updating AI models...")
        update_success = ai_system.update_models()
        
        if update_success:
            print("✅ AI models updated successfully!")
        else:
            print("ℹ️  Model update skipped (may need more data or ML libraries)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_comprehensive_intelligence():
    """Test comprehensive intelligence system"""
    print("\n" + "="*50)
    print("🎯 Testing Comprehensive Intelligence System")
    print("اختبار نظام الذكاء الشامل")
    print("="*50)
    
    try:
        ai_system = RealAdvancedIntelligence(output_dir="test_ai_output")
        
        print("\n[*] Running comprehensive intelligence test...")
        
        # Collect data
        behavioral_data = ai_system.collect_behavioral_data()
        threat_indicators = ai_system.collect_threat_indicators()
        
        # Make predictions
        predictions = ai_system.predict_threats()
        
        # Make decisions
        if predictions:
            decisions = ai_system.make_intelligent_decisions(predictions)
        
        # Generate report
        report = ai_system.generate_intelligence_report()
        
        if report:
            print(f"✅ Comprehensive intelligence test completed!")
            print(f"   System Status: {report.get('system_status', {})}")
            print(f"   AI Models: {report.get('ai_models_status', {})}")
            print(f"   Data Points: {len(report.get('recent_activity', {}).get('last_behavioral_data', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Advanced Intelligence Module Test Suite")
    print("مجموعة اختبارات وحدة الذكاء المتقدم")
    print("="*60)
    print("⚠️  FOR AUTHORIZED TESTING ONLY")
    print("⚠️  للاختبارات المصرح بها فقط")
    print("="*60)
    
    # Run tests
    tests = [
        ("Behavioral Data Collection", test_behavioral_data_collection),
        ("Threat Indicators", test_threat_indicators),
        ("AI Predictions", test_ai_predictions),
        ("Intelligent Decisions", test_intelligent_decisions),
        ("NLP Analysis", test_nlp_analysis),
        ("Computer Vision", test_computer_vision),
        ("Model Updates", test_model_updates),
        ("Comprehensive Intelligence", test_comprehensive_intelligence)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[-] {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST RESULTS SUMMARY")
    print("ملخص نتائج الاختبارات")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Advanced Intelligence module is working correctly.")
        print("🎉 جميع الاختبارات نجحت! وحدة الذكاء المتقدم تعمل بشكل صحيح.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Check the errors above.")
        print(f"⚠️  {total-passed} اختبار فشل. تحقق من الأخطاء أعلاه.")
    
    print(f"\n📁 Test output saved to: test_ai_output/")
    print(f"📁 مخرجات الاختبار محفوظة في: test_ai_output/")
    
    return results


if __name__ == "__main__":
    main()
