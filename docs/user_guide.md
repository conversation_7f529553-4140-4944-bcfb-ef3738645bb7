# دليل المستخدم الشامل - إطار عمل الأمان السيبراني

## 📋 جدول المحتويات

1. [مقدمة](#مقدمة)
2. [التثبيت والإعداد](#التثبيت-والإعداد)
3. [البدء السريع](#البدء-السريع)
4. [الوحدات الأساسية](#الوحدات-الأساسية)
5. [الوحدات المتقدمة](#الوحدات-المتقدمة)
6. [الذكاء الاصطناعي](#الذكاء-الاصطناعي)
7. [الواجهة الرسومية](#الواجهة-الرسومية)
8. [واجهة برمجة التطبيقات](#واجهة-برمجة-التطبيقات)
9. [الأمان والقانون](#الأمان-والقانون)
10. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 مقدمة

إطار عمل الأمان السيبراني هو منصة شاملة ومتقدمة لاختبار الاختراق والأمان السيبراني. يوفر الإطار مجموعة واسعة من الأدوات والوحدات للاستطلاع والاستغلال والتحليل الذكي.

### ✨ الميزات الرئيسية

- **🔍 استطلاع شامل**: وحدات متقدمة لجمع المعلومات
- **⚔️ استغلال متطور**: تقنيات حديثة لاختبار الثغرات
- **🧠 ذكاء اصطناعي**: تحليل ذكي وتوصيات تلقائية
- **🎨 واجهة سهلة**: واجهة رسومية وAPI متكامل
- **🔒 أمان عالي**: تشفير وحماية للبيانات الحساسة

### ⚠️ تحذير قانوني

**هذا الإطار مخصص للاستخدام التعليمي واختبارات الأمان المصرح بها فقط. يجب الحصول على إذن كتابي قبل استخدامه على أي نظام.**

---

## 🛠️ التثبيت والإعداد

### متطلبات النظام

- **نظام التشغيل**: Windows 10+, Linux (Ubuntu 18.04+), macOS 10.15+
- **Python**: الإصدار 3.8 أو أحدث
- **الذاكرة**: 4GB RAM كحد أدنى (8GB مُوصى به)
- **مساحة القرص**: 2GB مساحة فارغة

### خطوات التثبيت

#### 1. تحميل المشروع

```bash
git clone https://github.com/security-framework/security-framework.git
cd security-framework
```

#### 2. إنشاء بيئة افتراضية

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

#### 3. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

#### 4. إعداد قاعدة البيانات

```bash
python setup_database.py
```

#### 5. إعداد الإعدادات

```bash
cp config/config.example.yaml config/config.yaml
# قم بتحرير الملف وإضافة مفاتيح API الخاصة بك
```

### إعداد مفاتيح API

قم بتحرير ملف `config/config.yaml` وأضف مفاتيح API:

```yaml
apis:
  shodan:
    api_key: "YOUR_SHODAN_API_KEY"
  virustotal:
    api_key: "YOUR_VIRUSTOTAL_API_KEY"
  haveibeenpwned:
    api_key: "YOUR_HIBP_API_KEY"
```

---

## 🚀 البدء السريع

### تشغيل الواجهة الرسومية

```bash
python gui/main_interface.py
```

### تشغيل API

```bash
python api/rest_api.py
```

### استخدام سطر الأوامر

```bash
python main.py --target *********** --module basic_recon
```

### مثال سريع

```python
from modules.reconnaissance.basic_recon import BasicReconModule

# إنشاء وحدة الاستطلاع
recon = BasicReconModule()
recon.set_authorization(True, True)

# تنفيذ الاستطلاع
results = recon.start_execution("***********")
print(results)
```

---

## 🔍 الوحدات الأساسية

### وحدة الاستطلاع الأساسي

تقوم بجمع المعلومات الأساسية عن الهدف:

```python
from modules.reconnaissance.basic_recon import BasicReconModule

recon = BasicReconModule()
recon.set_authorization(True, True)

# استطلاع IP
results = recon.start_execution("*******")

# استطلاع نطاق
results = recon.start_execution("example.com")
```

**المعلومات المجمعة:**
- معلومات DNS
- فحص المنافذ
- معلومات الخدمات
- الموقع الجغرافي

### وحدة فحص الثغرات

```python
from modules.vulnerability.vulnerability_scanner import VulnerabilityScanner

scanner = VulnerabilityScanner()
scanner.set_authorization(True, True)

results = scanner.start_execution("*************")
```

**أنواع الثغرات المكتشفة:**
- ثغرات نظام التشغيل
- ثغرات الخدمات
- إعدادات غير آمنة
- كلمات مرور ضعيفة

---

## 🎯 الوحدات المتقدمة

### وحدة OSINT المتقدمة

تستخدم مصادر خارجية لجمع معلومات شاملة:

```python
from modules.reconnaissance.advanced_osint import AdvancedOSINTModule

config = {
    'shodan_api_key': 'YOUR_API_KEY',
    'virustotal_api_key': 'YOUR_API_KEY'
}

osint = AdvancedOSINTModule(config)
osint.set_authorization(True, True)

# تحليل IP
results = osint.start_execution("*******")

# تحليل بريد إلكتروني
results = osint.start_execution("<EMAIL>")
```

### وحدة التهرب المتقدم

تطبق تقنيات متقدمة لتجنب الكشف:

```python
from modules.evasion.advanced_evasion import AdvancedEvasionModule

evasion = AdvancedEvasionModule()
evasion.set_authorization(True, True)

results = evasion.start_execution("local")
```

**تقنيات التهرب:**
- كشف بيئات المحاكاة
- تجنب مضادات الفيروسات
- تشويش حركة المرور
- تقليد السلوك الطبيعي

### وحدة استغلال الويب

تختبر ثغرات تطبيقات الويب:

```python
from modules.exploitation.web_exploitation import WebExploitationModule

exploit = WebExploitationModule()
exploit.set_authorization(True, True)

results = exploit.start_execution("http://testsite.com")
```

**الثغرات المختبرة:**
- SQL Injection
- Cross-Site Scripting (XSS)
- Local File Inclusion (LFI)
- Command Injection
- Directory Traversal

---

## 🧠 الذكاء الاصطناعي

### محرك التحليل الذكي

يوفر تحليلاً متقدماً باستخدام الذكاء الاصطناعي:

```python
from modules.intelligence.ai_engine import AISecurityEngine

ai_engine = AISecurityEngine()
ai_engine.set_authorization(True, True)

# تقييم الثغرات
vulnerability_data = {
    'vulnerabilities': [
        {
            'cve_id': 'CVE-2023-0001',
            'cvss_score': 9.8,
            'exploit_available': True
        }
    ]
}

results = ai_engine.start_execution(
    "vulnerability_assessment",
    vulnerability_data=vulnerability_data
)
```

### التنبؤ بنجاح الاستغلال

```python
exploit_data = {
    'target_type': 'web_application',
    'defenses': ['firewall', 'waf'],
    'attacker_skill': 'expert',
    'exploit_complexity': 'medium'
}

results = ai_engine.start_execution(
    "exploit_prediction",
    exploit_data=exploit_data
)

print(f"احتمالية النجاح: {results['data']['exploit_prediction']['success_probability']:.2%}")
```

### التحليل الشامل

```python
# تحليل شامل لجميع البيانات
results = ai_engine.start_execution(
    "comprehensive_analysis",
    vulnerability_data=vulnerability_data,
    exploit_data=exploit_data,
    threat_data=threat_data,
    network_data=network_data
)
```

---

## 🎨 الواجهة الرسومية

### تشغيل الواجهة

```bash
python gui/main_interface.py
```

### الميزات الرئيسية

#### 1. لوحة التحكم
- إعداد الأهداف
- اختيار نوع العملية
- مراقبة التقدم

#### 2. عرض النتائج
- شجرة النتائج التفاعلية
- تفاصيل مفصلة لكل نتيجة
- تصدير البيانات

#### 3. الرسوم البيانية
- إحصائيات العمليات
- توزيع الثغرات
- تحليل المخاطر

#### 4. إدارة الجلسات
- إنشاء جلسات جديدة
- حفظ واستعادة الجلسات
- تتبع التاريخ

### استخدام الواجهة

1. **إنشاء جلسة جديدة**: ملف → جلسة جديدة
2. **إدخال الهدف**: في حقل "الهدف"
3. **اختيار نوع العملية**: من القائمة المنسدلة
4. **بدء التنفيذ**: النقر على "بدء الاستطلاع" أو "بدء الاستغلال"
5. **مراجعة النتائج**: في تبويب "النتائج"

---

## 🌐 واجهة برمجة التطبيقات

### تشغيل API

```bash
python api/rest_api.py
```

الخادم سيعمل على: `http://localhost:5000`

### المصادقة

#### تسجيل الدخول

```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### استخدام مفتاح API

```bash
curl -X GET http://localhost:5000/api/sessions \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### العمليات الأساسية

#### إنشاء جلسة

```bash
curl -X POST http://localhost:5000/api/sessions \
  -H "Authorization: Bearer YOUR_API_KEY"
```

#### الاستطلاع الأساسي

```bash
curl -X POST http://localhost:5000/api/reconnaissance/basic \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"target": "*******", "session_id": "SESSION_ID"}'
```

#### استغلال الويب

```bash
curl -X POST http://localhost:5000/api/exploitation/web \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"target": "http://testsite.com", "confirmed": true}'
```

#### التحليل الذكي

```bash
curl -X POST http://localhost:5000/api/ai/analyze \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "vulnerability_assessment",
    "data": {
      "vulnerability_data": {
        "vulnerabilities": [...]
      }
    }
  }'
```

### مراقبة العمليات

#### حالة العملية

```bash
curl -X GET http://localhost:5000/api/operations/OPERATION_ID \
  -H "Authorization: Bearer YOUR_API_KEY"
```

#### إلغاء العملية

```bash
curl -X POST http://localhost:5000/api/operations/OPERATION_ID/cancel \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### الحصول على النتائج

```bash
curl -X GET http://localhost:5000/api/results/SESSION_ID \
  -H "Authorization: Bearer YOUR_API_KEY"
```

---

## 🔒 الأمان والقانون

### الاستخدام القانوني

⚠️ **تحذير مهم**: هذا الإطار مخصص للاستخدامات التالية فقط:

1. **الاختبارات المصرح بها**: على الأنظمة التي تملكها أو لديك إذن كتابي لاختبارها
2. **البيئات التعليمية**: في المختبرات والبيئات المخصصة للتعلم
3. **اختبارات الأمان الداخلية**: في بيئة الشركة بموافقة الإدارة

### المتطلبات القانونية

- **إذن كتابي**: احصل على موافقة كتابية قبل أي اختبار
- **نطاق محدد**: حدد بوضوح الأنظمة المسموح اختبارها
- **توقيت محدد**: اتفق على أوقات الاختبار
- **تقرير النتائج**: قدم تقريراً مفصلاً بالنتائج

### أفضل الممارسات الأمنية

#### حماية البيانات

```python
# استخدم التشفير دائماً
from core.security_manager import security_manager

# تشفير البيانات الحساسة
encrypted_data = security_manager.encrypt_data("sensitive_info")

# فك التشفير عند الحاجة
decrypted_data = security_manager.decrypt_data(encrypted_data)
```

#### إدارة الجلسات

```python
# إنشاء جلسة آمنة
session_id = db_manager.create_session("user_id")

# إغلاق الجلسة عند الانتهاء
db_manager.close_session(session_id)
```

#### تسجيل العمليات

```python
# تسجيل جميع العمليات
module.log_activity("Operation started", data={"target": target})
```

---

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة

#### 1. خطأ في الاتصال بقاعدة البيانات

**المشكلة**: `Database connection failed`

**الحل**:
```bash
# تحقق من وجود ملف قاعدة البيانات
ls data/security_framework.db

# إعادة إنشاء قاعدة البيانات
python setup_database.py
```

#### 2. خطأ في مفاتيح API

**المشكلة**: `API key validation failed`

**الحل**:
```yaml
# تحقق من صحة مفاتيح API في config.yaml
apis:
  shodan:
    api_key: "VALID_API_KEY"
```

#### 3. خطأ في الأذونات

**المشكلة**: `Authorization required`

**الحل**:
```python
# تأكد من تعيين الأذونات
module.set_authorization(True, True)
```

### تشخيص المشاكل

#### تفعيل التسجيل المفصل

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### فحص حالة النظام

```bash
# فحص صحة API
curl http://localhost:5000/api/health

# فحص قاعدة البيانات
python -c "from core.database_manager import db_manager; print(db_manager.get_database_statistics())"
```

#### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
python tests/test_comprehensive.py

# تشغيل اختبار محدد
python -m unittest tests.test_comprehensive.TestCoreComponents
```

### الحصول على المساعدة

#### السجلات

```bash
# عرض السجلات
tail -f logs/security_framework.log
```

#### معلومات النظام

```python
from core.config_manager import config
print(config.get_system_info())
```

#### الإبلاغ عن المشاكل

عند الإبلاغ عن مشكلة، يرجى تضمين:

1. **وصف المشكلة**: ما الذي حدث؟
2. **خطوات الإعادة**: كيف يمكن إعادة إنتاج المشكلة؟
3. **رسائل الخطأ**: أي رسائل خطأ ظهرت؟
4. **معلومات النظام**: نظام التشغيل وإصدار Python
5. **السجلات**: ملفات السجل ذات الصلة

---

## 📚 موارد إضافية

### الوثائق التقنية

- [دليل المطور](developer_guide.md)
- [مرجع API](api_reference.md)
- [دليل التثبيت المتقدم](advanced_installation.md)

### أمثلة عملية

- [أمثلة الاستطلاع](examples/reconnaissance_examples.py)
- [أمثلة الاستغلال](examples/exploitation_examples.py)
- [أمثلة الذكاء الاصطناعي](examples/ai_examples.py)

### المجتمع والدعم

- **GitHub**: [رابط المشروع](https://github.com/security-framework)
- **الوثائق**: [موقع الوثائق](https://docs.security-framework.com)
- **المنتدى**: [منتدى المجتمع](https://forum.security-framework.com)

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](../LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

نشكر جميع المساهمين والمطورين الذين ساعدوا في تطوير هذا الإطار.

---

**تذكر**: استخدم هذا الإطار بمسؤولية وفقط للأغراض القانونية والتعليمية. 🔒
