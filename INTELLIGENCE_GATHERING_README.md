# Intelligence Gathering Module - وحدة جمع المعلومات الاستخباراتية

## Overview - نظرة عامة

This module provides **real** intelligence gathering capabilities for authorized security testing and penetration testing. It replaces simulation functions with actual system interaction to collect genuine intelligence data.

هذه الوحدة توفر قدرات **حقيقية** لجمع المعلومات الاستخباراتية للاختبارات الأمنية المصرح بها واختبارات الاختراق. تستبدل وظائف المحاكاة بتفاعل حقيقي مع النظام لجمع بيانات استخباراتية حقيقية.

## ⚠️ WARNING - تحذير

**FOR AUTHORIZED TESTING ONLY**
**للاختبارات المصرح بها فقط**

This tool is designed for:
- Authorized penetration testing
- Security assessments with proper authorization
- Educational purposes in controlled environments
- Red team exercises with explicit permission

هذه الأداة مصممة لـ:
- اختبارات الاختراق المصرح بها
- التقييمات الأمنية مع التصريح المناسب
- الأغراض التعليمية في بيئات محكومة
- تمارين الفريق الأحمر مع إذن صريح

## Features - الميزات

### 1. System Intelligence - معلومات النظام
- **Basic System Info**: OS, hardware, architecture
- **Hardware Details**: CPU, memory, disk, network interfaces
- **Network Configuration**: Interfaces, routing, DNS, ARP tables
- **Security Software Detection**: Antivirus, firewall, EDR solutions
- **Installed Software Enumeration**: Registry-based (Windows) or package manager-based (Linux)
- **Running Processes**: Real-time process enumeration
- **System Services**: Service status and configuration
- **User Accounts**: Local user enumeration

### 2. Credentials Collection - جمع بيانات الاعتماد
- **Browser Passwords**: Chrome, Firefox, Edge password extraction with decryption
- **WiFi Passwords**: Stored wireless network credentials
- **Windows Credentials**: Credential Manager stored credentials
- **SSH Keys**: Private/public key discovery
- **Configuration Files**: Scanning for embedded credentials
- **Environment Variables**: Secret extraction from environment

### 3. File System Intelligence - معلومات نظام الملفات
- **Interesting Files**: Automated discovery of sensitive files
- **Metadata Extraction**: File timestamps, sizes, hashes
- **Credential Scanning**: Content analysis for embedded secrets
- **Encryption Detection**: Identification of encrypted files

### 4. Network Intelligence - معلومات الشبكة
- **Active Connections**: Real-time network connections
- **Listening Ports**: Open port enumeration
- **Network Shares**: SMB/CIFS share discovery
- **WiFi Networks**: Available and stored wireless networks
- **Routing Tables**: Network routing information
- **ARP Tables**: Address resolution protocol data

## Installation - التثبيت

### Prerequisites - المتطلبات المسبقة

```bash
# Install required Python packages
pip install psutil

# Windows-specific (for credential decryption)
pip install pywin32

# Optional: For enhanced functionality
pip install sqlite3
```

### Files - الملفات

- `intelligence_gathering.py` - Main module
- `test_intelligence_gathering.py` - Test suite
- `INTELLIGENCE_GATHERING_README.md` - This documentation

## Usage - الاستخدام

### Basic Usage - الاستخدام الأساسي

```python
from intelligence_gathering import RealIntelligenceGatherer

# Initialize the gatherer
gatherer = RealIntelligenceGatherer(output_dir="intelligence_data")

# Run comprehensive intelligence gathering
results = gatherer.run_comprehensive_intelligence_gathering()
```

### Individual Components - المكونات الفردية

```python
# System intelligence only
system_intel = gatherer.collect_system_intelligence()

# Credentials only
credentials = gatherer.collect_credentials()

# Specific credential types
browser_passwords = gatherer.extract_browser_passwords()
wifi_passwords = gatherer.extract_wifi_passwords()
ssh_keys = gatherer.find_ssh_keys()
```

### Running Tests - تشغيل الاختبارات

```bash
python test_intelligence_gathering.py
```

## Output - المخرجات

### Database Storage - تخزين قاعدة البيانات
- SQLite database: `intelligence_data/intelligence.db`
- Tables: `system_info`, `credentials`, `network_intelligence`, `file_intelligence`

### JSON Reports - تقارير JSON
- Comprehensive reports saved as JSON files
- Timestamped for tracking multiple runs
- Structured data for easy parsing

### File Structure - هيكل الملفات
```
intelligence_data/
├── intelligence.db          # SQLite database
├── intelligence_report_*.json  # JSON reports
└── logs/                   # Operation logs
```

## Technical Details - التفاصيل التقنية

### Real Implementation Changes - تغييرات التنفيذ الحقيقي

1. **Browser Password Decryption**:
   - Windows: Uses `win32crypt.CryptUnprotectData()`
   - Linux: Requires keyring access (simplified implementation)
   - Chrome/Edge: Direct SQLite database access with decryption

2. **WiFi Password Extraction**:
   - Windows: `netsh wlan show profile key=clear`
   - Linux: NetworkManager configuration files

3. **System Information**:
   - Real WMI queries on Windows
   - Direct `/proc` filesystem access on Linux
   - Actual registry enumeration for software

4. **Network Intelligence**:
   - Live network connection monitoring
   - Real routing table extraction
   - Actual ARP table data

### Security Considerations - الاعتبارات الأمنية

- **Privilege Requirements**: Some functions require elevated privileges
- **Antivirus Detection**: May trigger security software alerts
- **Audit Trails**: Operations may be logged by system security
- **Data Sensitivity**: Collected data contains sensitive information

## Platform Support - دعم المنصات

### Windows
- ✅ Full feature support
- ✅ Registry access for software enumeration
- ✅ WMI queries for hardware information
- ✅ Credential Manager access
- ✅ Browser password decryption

### Linux
- ✅ Most features supported
- ✅ Package manager integration
- ✅ `/proc` filesystem access
- ⚠️ Limited credential decryption (keyring dependent)
- ✅ NetworkManager WiFi extraction

### macOS
- ⚠️ Limited support (basic system info only)
- ❌ Credential extraction not implemented

## Legal and Ethical Use - الاستخدام القانوني والأخلاقي

### Authorized Use Only - الاستخدام المصرح به فقط

This tool must only be used in the following scenarios:
- Penetration testing with written authorization
- Security assessments on systems you own
- Educational environments with proper supervision
- Red team exercises with explicit permission

### Prohibited Uses - الاستخدامات المحظورة

- Unauthorized access to systems
- Data theft or espionage
- Malicious activities
- Violation of privacy laws

## Troubleshooting - استكشاف الأخطاء وإصلاحها

### Common Issues - المشاكل الشائعة

1. **Permission Denied Errors**:
   - Run with elevated privileges
   - Check file/directory permissions

2. **Missing Dependencies**:
   - Install required packages: `pip install psutil pywin32`

3. **Antivirus Interference**:
   - Add exclusions for testing directory
   - Temporarily disable real-time protection (authorized testing only)

4. **Database Errors**:
   - Ensure write permissions to output directory
   - Check disk space availability

### Debug Mode - وضع التصحيح

Enable verbose logging by modifying the gatherer initialization:
```python
gatherer = RealIntelligenceGatherer(output_dir="intelligence_data", debug=True)
```

## Contributing - المساهمة

When contributing to this module:
1. Ensure all changes maintain security best practices
2. Add appropriate error handling
3. Update documentation for new features
4. Test on multiple platforms when possible
5. Follow ethical guidelines for security tools

## License - الترخيص

This tool is provided for educational and authorized testing purposes only. Users are responsible for ensuring compliance with all applicable laws and regulations.

---

**Remember: With great power comes great responsibility. Use this tool ethically and legally.**
**تذكر: مع القوة العظيمة تأتي المسؤولية العظيمة. استخدم هذه الأداة بشكل أخلاقي وقانوني.**
